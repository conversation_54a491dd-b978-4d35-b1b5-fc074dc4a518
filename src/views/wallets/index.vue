<template>
    <ComPageHeader title="wallet_deposit" :type="1" @close="handleBack" />
    <div class="wallet">
        <walletHead title="Home_Wallets" />
        <div class="wallet-content">
            <div v-if="allMethods.length" class="wallet-title">{{ $t('Method') }}</div>
            <ul v-if="allMethods.length" class="select-list1 select-list-icon">
                <li
                    :class="['list-item list-item-icon', { active: curMethod === item.name }, { status: item.status !== 1 }]"
                    v-for="item in allMethods"
                    :key="item.id"
                    @click="changeMethod(item)"
                >
                    <!-- {{ item.name }} -->
                    <img :src="item.icon" />
                </li>
            </ul>
            <div class="wallet-title">{{ $t('Wallet_deposit_method') }}</div>
            <ul class="select-list1">
                <li
                    :class="['list-item', { active: curChannel === item.channel }, { status: getChannelInfo(item.channel).status !== 1 }]"
                    v-for="item in curMethodChannels"
                    :key="item.id"
                    @click="changeChannel(item)"
                >
                    {{ getChannelInfo(item.channel).name }}
                </li>
            </ul>
            <div class="wallet-title">{{ $t('Deposit_Amount') }}</div>
            <ul class="select-list">
                <li
                    :class="['list-item amount-num', { active: accountIndex === index }]"
                    v-for="(item, index) in checkProudct"
                    :key="item.id"
                    @click="changeAccount(item, index)"
                >
                    {{ formatLocalMoney(item.cny, 0) }}

                    <div v-if="activityList[activeIdx]?.key === 'bonusstore' || item.gifts?.gold || item.gifts?.bonus" class="give-tip">
                        <div class="tip-wrap">
                            <div class="gift-num">+{{ getGiftsNum(item) }}</div>
                        </div>
                    </div>
                </li>
            </ul>
            <div v-if="showPromotion" class="activity-title">{{ $t('Available Bonuses') }}</div>
            <!-- <ul v-if="showPromotion" class="activity-list">
                    <li
                        :class="['activity-item', { active: index == activeIdx }]"
                        v-for="(item, index) in activityList" 
                        :key="index"
                        @click="changeActive( index)"
                    >
                        {{ $t(item.name) }}
                    </li>
                </ul> -->
            <div class="channel-Btn">
                <div v-if="activityList[0]?.key === 'firstpayment'" :class="['fisrt_btn', { active: activeIdx == 1 }]" @click="changeActive()">
                    <div class="channel-title">{{ $t('First Deposit') }}</div>
                    <div
                        class="channel-content"
                        v-html="
                            $t('Fisrt_deposit_info1', {
                                current_type: store.cureencySymbol(),
                                current_number: activityList[0].data ? activityList[0].data?.maxNumber : 0,
                            })
                        "
                    ></div>
                    <van-checkbox :checked="activeIdx == 0" class="custom-checkbox" @change="changeActive" />
                </div>

                <div v-if="activityList[0]?.key === 'bonusstore'" :class="['bonus_btn', { active: activeIdx == 1 }]" @click="changeActive()">
                    <div class="channel-title">{{ $t('Recharge and Get Extra') }}</div>
                    <div class="channel-content">
                        <span>{{ shoppayment?.goldratio * 100 }}% </span>Cash + <span>{{ shoppayment?.bonusratio * 100 }}%</span> Bonus
                    </div>
                    <van-checkbox :checked="activeIdx == 0" class="custom-checkbox" @change="changeActive" />
                </div>
            </div>

            <!-- <div
                    v-if="tipType === 2"
                    class="first-rechage"
                    v-html="
                        $t('Fisrt_deposit_upto', {
                            current_type: store.cureencySymbol(),
                            current_number: activityList[activeIdx].data.maxNumber,
                        })
                    "
                ></div> -->
            <div class="will-receive">
                <div>You Will Get</div>
                <div v-html="getWillText()"></div>
            </div>
            <div class="wallet-btn">
                <div :class="['btn', { disable: accountIndex === -1 || cantDepose }]" @click="handleSubmit">
                    {{ $t('Deposit') }} {{ store.cureencySymbol() }}{{ checkProudct[accountIndex]?.cny || '' }}
                </div>
                <div
                    v-if="vipInfo.curLevel < 1 && wallet?.bonus > 0"
                    class="tip"
                    v-html="
                        $t('bonus_tovip_info', {
                            number1: vipInfo?.rechargeMax - vipInfo?.recharge || 1,
                            current_type: store.cureencySymbol(),
                            number2: wallet?.bonus || 0,
                        })
                    "
                ></div>
            </div>
            <div class="times_tip">
                <p
                    class="text-left pb-[0.2rem]"
                    v-html="
                        activityList[activeIdx]?.key === 'firstpayment'
                            ? $t('Bonus_Deposit_info', { times: checkProudct[accountIndex]?.flowmult || 1 })
                            : activityList[activeIdx]?.key === 'bonusstore'
                            ? $t('Bonus_Deposit_info', { times: shoppayment?.flowmult || 1 })
                            : ''
                    "
                ></p>
            </div>
        </div>
        <Server />
        <ProductChannel v-model="showChannel" @sendChannel="getChannel" />
        <Lamp v-if="cantDepose" :channel="channel" tip="wallet_deposit_roll" />
        <!-- <BaseFooter :initTab="3" /> -->
        <van-overlay class="flex flex-col items-center justify-center" :show="showLoading" :z-index="6000">
            <div><van-loading type="spinner" /></div>
            <div class="loading-tip">{{ $t('Order_is_being_gener') }}</div>
        </van-overlay>
    </div>
</template>
<script setup lang="ts">
import walletHead from '@/components/wallet/walletHeader.vue'
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { getGamestore } from '@/api/wallet'
import { Gamestore } from '@/api/wallet/type'
import ProductChannel from '@/components/dialog/productChannel.vue'
import { Log } from '@/api/log'
import { formatLocalMoney } from '@/utils/toolsValidate'
import Server from '@/components/server.vue'
import Lamp from '@/components/wallet/walletLamp.vue'
import EventBus from '@/utils/bus'
import dayjs from 'dayjs'
import ComPageHeader from '@/components/common/ComPageHeader.vue'
import { useGame } from '@/hooks/useGame'
import { useI18n } from 'vue-i18n'
import { createPaysoOrder } from '@/api/wallet'
import { getDeviceInfo } from '@/utils'
import webApp from '@twa-dev/sdk'
const i18n = useI18n()
const router = useRouter()
const route = useRoute()
const { gameSubscibe } = useGame()

const store = useBaseStore()
const { userInfo, wallet, vipInfo } = storeToRefs(store)

const originList = ref([])
const reliefRecharge = ref()
const shoppayment = ref()
const accountIndex = ref(0)
const showChannel = ref(false)
const showLoading = ref(false)
const channel = ref([])
const allMethods = ref([])
const allChannels = ref([])
const curMethod = ref('')
const curChannel = ref('')
const init = [
    {
        type: 1,
        name: 'Regular_deposit',
        key: 'store',
    },
]

const bonusStore = [
    {
        type: 3,
        name: 'Bonus_deposit',
        key: 'bonusstore',
    },
]

const activityList = ref([...init])
const checkEventList = [
    {
        type: 2,
        name: 'Fisrt_deposit',
        key: 'firstpayment',
        status: 1,
        data: {
            maxNumber: 0,
            maxMult: 0,
        },
        check(result) {
            const { configs = {} } = result.acticity.meta || {}
            const values = Object.values(configs).map((item) => {
                const { gold = 0, bonus = 0 } = item?.gifts || {}
                return gold + bonus
            })
            const maxValue = Math.max(...values)
            const maxItem = Object.values(configs).find((item) => {
                const { gold = 0, bonus = 0 } = item?.gifts || {}
                return gold + bonus === maxValue
            })
            this.data.maxNumber = maxValue
            this.data.maxMult = maxItem?.flowmult || 0
            return this.status === result.acticity.status
        },
    },
]

const uid = computed(() => {
    return store.userInfo.uid
})

const activeIdx = ref(0)
const showPromotion = ref(false)
const handleBack = async () => {
    if (route.query.from === 'game') {
        const url = await gameSubscibe(store.prevGameTag)
        router.replace({
            path: '/iframe',
            query: {
                u: encodeURIComponent(url),
                from: 'wallet',
            },
        })
    } else {
        router.back()
    }
}

const orderParams = reactive({
    item: {},
    source: 'wallet',
    bflowmult: false, //是否使用流量倍率
})
provide('orderParams', orderParams)

const cantDepose = computed(() => {
    let channels = []
    if (curMethod.value) {
        let method = allMethods.value.find((item) => item.name === curMethod.value)
        channels = method?.channels || []
        console.log(channels)
    } else {
        channels = allChannels.value
    }

    return (
        channels &&
        channels.length &&
        channels.every((item) => {
            getChannelInfo(item.channel)?.status === 0
        })
    )
})
const checkProudct = computed(() => {
    const active = activityList.value[activeIdx.value]

    let list =
        originList.value?.filter((item) => (active.key === 'store' || active.key === 'bonusstore' ? item.instore : item.usefor === active.key)) || []
    orderParams.source = list[0]?.usefor || 'wallet'

    orderParams.bflowmult = active.key === 'bonusstore'

    if (curChannel.value) {
        list = list.filter((item) => {
            const productList = getChannelInfo(curChannel.value)?.productList || []
            return productList.includes(item.id)
        })
    }
    changeAccount(list[0], 0)
    return list
})

const curMethodChannels = computed(() => {
    if (!allMethods.value.length) {
        curMethod.value = ''
        // if (!curChannel.value) {
        //     //选择其中一个status为1的
        //     curChannel.value = allChannels.value.find((item) => item.status === 1)?.channel
        // }
        return allChannels.value
    }

    if (!curMethod.value) return []
    let method = allMethods.value.find((item) => item.name === curMethod.value)
    // if (method?.channels.length && !curChannel.value) {
    //     //选择其中一个status为1的
    //     curChannel.value = method?.channels.find((item) => getChannelInfo(item.channel).status === 1)?.channel
    // }

    return method?.channels
})

const getChannelInfo = (channel: string) => {
    return allChannels.value.find((item) => item.channel === channel)
}

const tipType = computed(() => {
    return activityList.value[activeIdx.value]?.type || 1
})

watch(
    () => store.wallet?.currency,
    (oldVal, newVal) => {
        if (oldVal === newVal) {
            return
        }

        getProduct()
        getVipInfo()
    }
)

const getWillText = () => {
    let text = ''
    const item = checkProudct.value[accountIndex.value]
    if (!item) return ''
    if (activityList.value[activeIdx.value]?.key === 'bonusstore') {
        const cashnum = Math.floor((shoppayment.value?.goldratio + 1) * item.worth?.gold)
        const bonusnum = Math.floor(shoppayment.value?.bonusratio * item.worth?.gold)
        if (cashnum > 0) {
            text += 'Cash <span>' + cashnum + '</span>'
        }
        if (bonusnum > 0) {
            text += ' + Bonus <span>' + bonusnum + '</span>'
        }
    } else {
        // const cashnum = getGiftsNum(item) + item.worth?.gold
        const { gold = 0, bonus = 0 } = item.gifts
        text = 'Cash <span>' + (gold + +item.worth?.gold) + '</span>'
        if (bonus > 0) {
            text += ' + Bonus <span>' + bonus + '</span>'
        }
    }

    return text
}

const getGiftsNum = (item) => {
    const { gold = 0, bonus = 0 } = item.gifts
    if (activityList.value[activeIdx.value]?.key === 'bonusstore') {
        return Math.floor(item.worth?.gold * shoppayment.value?.goldratio + item.worth?.gold * shoppayment.value?.bonusratio)
    }

    return gold + bonus
}

const getProduct = async () => {
    try {
        const productRes: Gamestore = await getGamestore(userInfo.value.uid)
        console.log('productRes', productRes)

        if (productRes.code === 200) {
            curMethod.value = ''
            curChannel.value = ''
            originList.value = (productRes as Gamestore)?.items
            reliefRecharge.value = productRes.reliefRecharge
            shoppayment.value = productRes.shoppayment
            allMethods.value = productRes.methods
            // if (allMethods.value.length && !curMethod.value) {
            //     curMethod.value = allMethods.value.find((item) => item.status === 1)?.name || allMethods.value[0].name
            //     // curMethod.value = allMethods.value[0].name
            // }
            allChannels.value = productRes.channels
            await check()
            checkDefaultChannel()
        }
    } catch (err) {
        console.log(err)
    }
}

const changeMethod = (item) => {
    if (item.status !== 1) {
        showToast(i18n.t('wallet.Maintenance'))
        return
    }
    curChannel.value = ''
    curMethod.value = item.name
    localStorage.setItem(
        'lastDepositChannel' + store.wallet?.currency,
        JSON.stringify({ method: curMethod.value, channel: curChannel.value, activeIdx: activeIdx.value })
    )
}

const changeChannel = (item) => {
    if (getChannelInfo(item.channel).status !== 1) {
        showToast(i18n.t('wallet.Maintenance'))
        return
    }
    curChannel.value = item?.channel
    localStorage.setItem(
        'lastDepositChannel' + store.wallet?.currency,
        JSON.stringify({ method: curMethod.value, channel: curChannel.value, activeIdx: activeIdx.value })
    )
}
const changeAccount = (item, index) => {
    accountIndex.value = index
    orderParams.item = item
    // Log({ event: `wallet_${store.wallet.currency}_${item.cny}` })
}
/**
 * Create a deposit order.
 * @fires {Log} `wallet_deposit_submit`
 * @param {Object} product - The product to deposit.
 * @param {number} product.id - The id of the product.
 * @param {string} product.usefor - The source of the deposit.
 * @param {boolean} orderParams.bflowmult - Whether to use the flow multiplier.
 * @returns {Promise<void>}
 */
const handleSubmit = async () => {
    Log({ event: 'wallet_deposit_submit' })
    showLoading.value = true
    const product = orderParams.item
    try {
        const order = await createPaysoOrder({
            uid: store.userInfo.uid,
            productId: product?.id,
            channel: curChannel.value,
            osType: getDeviceInfo().os,
            tradeType: 'H5',
            redirectUrl: location.href,
            source: product?.usefor || '',
            bflowmult: !!orderParams?.bflowmult,
        })
        if (order.code === 200) {
            const url = order.body.guideUrl

            if (store.isTg) {
                if (url) {
                    webApp.openLink(url)
                }
                return
            }
            if (url) {
                // location.href = url
                showDialog({
                    title: i18n.t('Confirmation'),
                    message: i18n.t('wallet_deposit_confirm'),
                    confirmButtonText: 'Go',
                }).then(() => {
                    // ✅ 同步跳转（通常不会被拦截）
                    // window.location.href = url
                    window.open(url, '_blank')
                })
            }
        }
    } catch (e) {
        console.log(e)
    } finally {
        showLoading.value = false
    }
}
const getVipInfo = async (loading = true, tip = false) => {
    try {
        const info = await store.getVipInfo(loading, tip)
        if (info.code === 200) {
            store.setvipInfo(info)
        }
    } catch (e) {
        console.log(e)
    }
}
const getChannel = (channels) => {
    channel.value = channels
}
const changeActive = () => {
    activeIdx.value = activeIdx.value === 0 ? 1 : 0
    localStorage.setItem(
        'lastDepositChannel' + store.wallet?.currency,
        JSON.stringify({ method: curMethod.value, channel: curChannel.value, activeIdx: activeIdx.value })
    )
}
const checkEvents = (item) => {
    return store.getSignEvent({ id: item.key }, false, false)
}
const check = async () => {
    const list = checkEventList.map((item) => {
        return checkEvents(item)
    })
    const resultList = await Promise.all(list)
    const filterList = resultList
        .filter((result, index) => result.code === 200 && checkEventList[index].check(result))
        .map((item) => {
            const { key, name, type, data } = checkEventList.find((event) => event.key === item.acticity.name)
            return {
                key: key,
                name: name,
                type: type,
                data,
            }
        })

    if (filterList.length) {
        showPromotion.value = true
        activityList.value = [...filterList, ...init]
    } else {
        if (shoppayment.value) {
            const now = dayjs()
            if (now.isBefore(dayjs(shoppayment.value.end))) {
                activityList.value = [...bonusStore, ...init]
            } else {
                activityList.value = [...init]
            }
        } else {
            activityList.value = [...init]
        }
    }
}

const checkDefaultChannel = () => {
    let lastDepositChannel = localStorage.getItem('lastDepositChannel' + store.wallet?.currency)
        ? JSON.parse(localStorage.getItem('lastDepositChannel' + store.wallet?.currency))
        : {}
    curMethod.value = lastDepositChannel?.method
    if (!allMethods.value.length) curMethod.value = ''
    if (allMethods.value.length) {
        if (!curMethod.value) {
            curMethod.value = allMethods.value.find((item) => item.status === 1)?.name
        } else {
            const method = allMethods.value.find((item) => item.name === curMethod.value)
            if (method && method.status === 1) {
                curMethod.value = method.name
            }
        }
    }
    curChannel.value = lastDepositChannel?.channel
    if (!allChannels.value.length) curChannel.value = ''
    if (allChannels.value.length) {
        if (!curChannel.value) {
            curChannel.value = allChannels.value.find((item) => item.status === 1)?.channel
        } else {
            const channel = allChannels.value.find((item) => item.channel === curChannel.value)
            if (channel && channel.status === 1) {
                curChannel.value = channel.channel
            }
        }
    }

    if (activityList.value[0]?.key === 'firstpayment') {
        activeIdx.value = lastDepositChannel?.activeIdx || 0
    } else {
        activeIdx.value = lastDepositChannel?.activeIdx || 1
    }
    console.log('curChannel.value', activityList.value, activityList.value[0]?.key, curChannel.value, activeIdx.value, lastDepositChannel?.activeIdx)
    localStorage.setItem(
        'lastDepositChannel' + store.wallet?.currency,
        JSON.stringify({ method: curMethod.value, channel: curChannel.value, activeIdx: activeIdx.value })
    )
}

const handlePayFinish = () => {
    getProduct()
    getVipInfo()
    // check()
}
onBeforeMount(() => {
    EventBus.on('paymentFinish', handlePayFinish)
})
onActivated(() => {
    Log({ event: 'wallet_visit' })

    getProduct()
    getVipInfo()
    // check()
})
onBeforeUnmount(() => {
    EventBus.off('paymentFinish', handlePayFinish)
})
</script>
<style lang="scss" scoped>
@use '@/components/wallet/newCom.scss' as *;

.wallet {
    height: calc(var(--vh, 1vh) * 100 - var(--home-header));
    font-family: MicrosoftYaHei;
    overflow-y: auto;
    padding: 20px;

    .give-tip {
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: -12px;
        left: -10px;
        width: 100px;
        height: 29px;
        background: url('@/assets/img/wallets/conner_bg1.png') no-repeat;
        background-size: 100% 100%;

        .tip-wrap {
            @apply relative top-[-1px];
            font-family: 'Microsoft YaHei';

            .vip-level {
                position: relative;
                top: 2px;
                color: #f4ff60;
                font-size: 21px;
                font-weight: 700;
            }
            .gift-num {
                margin-left: 4px;
                font-size: 23px;
                font-weight: 600;
            }
            .tip-add {
                height: 20px;
            }
        }
    }
    .activity-title {
        margin: 22px 0;
        color: #fff;
        font-family: 'Microsoft YaHei';
        font-size: 30px;
        font-weight: 600;
    }
    .activity-list {
        @apply grid grid-cols-2 gap-[24px];
        .activity-item {
            @apply flex justify-center items-center relative;
            width: 308px;
            height: 72px;
            border-radius: 20px;
            border: 3px solid #373d4c;
            background: #444b5e;
            font-family: 'Microsoft YaHei';
            font-size: 30px;
            font-weight: 600;
            color: #fff;

            &.active {
                border-color: #10c580;
                background: rgba(16, 197, 128, 0.3);

                &::after {
                    position: absolute;
                    right: -1px;
                    bottom: -2px;
                    display: inline-block;
                    content: '';
                    width: 52px;
                    height: 52px;
                    background: url('@/assets/img/wallets/checked.png') no-repeat;
                    background-size: 100% 100%;
                }
            }
        }
    }
    .first-rechage {
        @apply flex items-center;
        margin: 20px 0 9px;
        color: #fff;
        font-family: 'Microsoft YaHei';
        font-size: 36px;
        font-style: normal;
        font-weight: 700;

        :deep(span) {
            color: #10c580;
        }
        &::before {
            display: inline-block;
            content: '';
            margin-right: 10px;
            width: 36px;
            height: 32px;
            background: url('@/assets/img/wallets/tip_mark.png') no-repeat;
            background-size: 100% 100%;
        }
    }
    .will-receive {
        @apply flex justify-between;
        margin: 20px 0 9px;
        color: #fff;
        font-family: 'Microsoft YaHei';
        font-size: 34px;
        font-style: normal;
        font-weight: 700;

        :deep(span) {
            color: #10c580;
        }
    }

    .tip-title {
        margin: 40px 0;
        color: #ffea76;
        font-family: 'Microsoft YaHei';
        font-size: 22px;
        font-weight: 400;
    }
    .tip {
        margin-top: 50px;
        font-size: 22px;
        font-weight: bold;
        line-height: 30px;
        color: #668292;
        width: auto;
        :deep(.text-red) {
            font-weight: bold;
            line-height: 43px;
            color: #fe588a;
        }
    }

    .times_tip {
        margin-top: 10px;
        font-size: 22px;
        font-weight: bold;
        line-height: 30px;
        color: #668292;
        :deep(.text-red) {
            font-weight: bold;
            line-height: 43px;
            color: #fe588a;
        }
    }

    .wallet-btn {
        position: relative;
        margin-top: 10px;
        .btn {
            border-radius: 100px;
            background: linear-gradient(90deg, #2aee88 0%, #9ae871 100%);
            color: #000;

            text-align: center;
            font-family: 'Microsoft YaHei';
            font-size: 44px;
            font-weight: 600;
        }
        .btn.disable {
            pointer-events: none;
            opacity: 0.6;
        }
        .tip {
            position: absolute;
            left: 50%;
            top: -170px;
            // width: 502px;
            height: 166px;
            padding: 20px 50px;
            background: url('@/assets/img/wallets/di.png') no-repeat;
            background-size: 100% 100%;
            transform: translateX(-50%);
            color: #fff;

            :deep(span) {
                color: #fcff1b;
            }
        }
    }
    .text-left {
        font-weight: bold;
    }

    .channel-Btn {
        display: relative;
        margin-top: 20px;
        font-family: 'Microsoft YaHei';
        font-size: 26px;
        font-weight: 600;
        .channel-title {
            margin-left: 20px;
            margin-top: 20px;
            color: #fff000;
        }
        .channel-content {
            margin-left: 20px;
            margin-top: -8px;
            font-size: 42px;
            font-weight: 1000;
            color: #fff;

            :deep(span) {
                color: #10c580;
            }
        }
        .fisrt_btn {
            @apply flex  flex-col;
            position: relative;
            width: 100%;
            height: 142px;
            border-radius: 20px;
            box-shadow: 0 0 0 5px #42b983;
            font-family: 'Microsoft YaHei';
            background: url('@/assets/img/wallets/event_img/first_y.avif') no-repeat;
            background-size: 100% 100%;

            &.active {
                background: url('@/assets/img/wallets/event_img/first_n.avif') no-repeat;
                background-size: 100% 100%;
                box-shadow: none;
            }
        }

        .bonus_btn {
            @apply flex  flex-col;
            position: relative;
            width: 100%;
            height: 142px;
            border-radius: 20px;
            box-shadow: 0 0 0 5px #42b983;
            font-family: 'Microsoft YaHei';
            background: url('@/assets/img/wallets/event_img/normal_y.avif') no-repeat;
            background-size: 100% 100%;

            &.active {
                background: url('@/assets/img/wallets/event_img/normal_n.avif') no-repeat;
                background-size: 100% 100%;
                box-shadow: none;
            }
        }

        .custom-checkbox {
            position: absolute;
            right: 3%;
            top: 50%;
            transform: translate(-50%, -50%) scale(1.5);

            :deep(.van-checkbox__icon .van-icon) {
                border-radius: 8px;
                background-color: #60646288; /* 选中背景色 */
            }

            :deep(.van-checkbox__icon--checked .van-icon) {
                border-color: #fff; /* 选中边框颜色 */
                background-color: #60646288; /* 选中背景色 */
                color: #07f279; /* 对勾颜色 */
                // font-size: 40px;
            }
        }
    }

    .loading-tip {
        margin-top: 10px;
        font-size: 27px;
        color: #fff;
    }
    .wallet-content {
        padding-bottom: 30px;
    }
}
</style>
