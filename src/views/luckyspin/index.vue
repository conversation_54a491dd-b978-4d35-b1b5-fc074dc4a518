<template>
    <van-popup
        class="channel-pop"
        v-model:show="show1"
        position="bottom"
        close-on-popstate
        round
        :z-index="3000"
        @closed="handleClose"
        v-bind="$attrs"
    >
        <div class="wallet-bonus">
            <div class="title">
                <div class="help_btn" @click="handleHelp"></div>
                <span>{{ $t('Deposit Spin') }}</span>
                <div class="close" @click="handleClose"></div>
            </div>
            <div class="spin-type-info">
                <div class="spintype">
                    <div
                        class="spintype-item"
                        v-for="(item, index) in spintype"
                        :key="index"
                        :class="{ active: index === sindex }"
                        @:click="handleSpinType(index)"
                        :style="getspintype(index)"
                    >
                        <div class="spintype-item-lock" :class="{ active: index === sindex }" v-if="spintypeislock(index)"></div>

                        <van-badge :content="rotations[index]?.times" v-if="showdot(index)" :offset="[-3, 3]">
                            <div class="spintype-item-icon"></div>
                        </van-badge>
                    </div>
                </div>
                <div class="deposit-tip" :style="{ backgroundColor: spintype[sindex].color }">
                    <div class="info-title">
                        {{ spintype[sindex].title.toLocaleUpperCase() }} <span> {{ curSymbol[wallet?.currency] }}{{ maxwin }}</span>
                    </div>
                    <div class="info-content">
                        Total Deposit: <span>{{ total }}</span>
                    </div>
                </div>
            </div>
            <div class="card-bar">
                <div class="bar-wrap">
                    <spinbar :num="barpronum" :target="100" />
                    <div class="bar-content" :style="barprostyle(0)">
                        <img :src="getSpinImage(sindex)" alt="2" />
                        <div class="pro-text">{{ rotations[sindex]?.targets[0] }}</div>
                    </div>
                    <div class="bar-content" :style="barprostyle(1)">
                        <img :src="getSpinImage(sindex)" alt="" />
                        <div class="pro-text">{{ rotations[sindex]?.targets[1] }}</div>
                    </div>
                </div>
                <div class="deposit-btn" @click="handleDeposit"><img src="@/assets/img/icon/icon-svg-deposit.svg" alt="Deposit Icon" />Deposit</div>
            </div>

            <Spinitem class="spinitem" ref="spinitem1" :prizes="rotations[sindex]?.rewards" :maxwin="maxwin" :startspinindex="startspinindex" />

            <div class="spin-btn">
                <van-button
                    class="spin-btn-item"
                    :style="{ background: !canSpin ? '#808285' : spintype[sindex].color }"
                    :class="{ disabled: !canSpin, shake: isShaking, 'text-red': isTextRed }"
                    @click="handleSpin"
                >
                    {{ spintype[sindex].title }} Spin: <span :class="{ 'text-red': canSpin }">{{ rotations[sindex]?.times }}</span>
                </van-button>
            </div>
        </div>
    </van-popup>

    <Luckyspinerule v-model="ruleshow" />
    <signaward v-model="signawardinfo.isshow" v-if="signawardinfo.isshow" :type="signawardinfo.type" :msg="signawardinfo.items" />
</template>
<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useBaseStore } from '@/stores'
import { storeToRefs } from 'pinia'
import eventBus from '@/utils/bus'
import popupManager from '@/utils/PopupManager'
import Spinitem from '@/components/luckyspin/spinitem.vue'
import Luckyspinerule from '@/components/luckyspin/luckyspinerule.vue'
import signaward from '@/components/dialog/signaward.vue'
import GoldPic from '@/assets/img/dialog/sign/cash.png'
import spinbar from '@/components/luckyspin/spinbar.vue'
const file = import.meta.glob('../../assets/img/luckyspin/*', { eager: true })
// import { backgroundSize } from 'html2canvas/dist/types/css/property-descriptors/background-size'

const props = defineProps({
    type: {
        type: Number,
        default: () => 0,
    },
})

const spintype = [
    {
        title: 'Lucky',
        color: 'rgb(226, 114, 52)',
        img_act: 'images/luckyspin/spin1_act.png',
        img: 'images/luckyspin/spin1_off.png',
    },
    {
        title: 'Super',
        color: '#ea9022',
        img_act: 'images/luckyspin/spin2_act.png',
        img: 'images/luckyspin/spin2_off.png',
    },
    { title: 'Mega', color: '#6a18dd', img_act: 'images/luckyspin/spin3_act.png', img: 'images/luckyspin/spin3_off.png' },
    {
        title: 'Power',
        color: '#dc5012',
        img_act: 'images/luckyspin/spin4_act.png',
        img: 'images/luckyspin/spin4_off.png',
    },
]

const show1 = defineModel()
const ruleshow = ref(false)
const router = useRouter()
const store = useBaseStore()
const { curSymbol, wallet, userInfo } = storeToRefs(store)

const spinitem1 = ref(null)
const rotations = ref([])
const total = ref(0)
const sindex = ref(0)
const startspinindex = ref(0)
const isRotating = ref(false)

const isShaking = ref(false)
const isTextRed = ref(false)

const signawardinfo = ref({
    isshow: false,
    type: 2,
    items: {},
})

const maxwin = computed(() => {
    if (!rotations.value[sindex.value]) return 0
    let maxwin = Math.max(...rotations.value[sindex.value].rewards.map((prize) => prize.num))
    return maxwin || 0
})

const spintitle = computed(() => {
    return spintype[sindex.value]?.title.toLocaleUpperCase() + ' SPIN'
})

const barpronum = computed(() => {
    const progress = rotations.value[sindex.value]?.progress
    const targets = rotations.value[sindex.value]?.targets
    if (progress) return (progress / targets[targets.length - 1]) * 100

    return 0
})

const barprostyle = computed(() => {
    return (index: number) => {
        const targets = rotations.value[sindex.value]?.targets
        const progress = rotations.value[sindex.value]?.progress
        const left = targets.length > 1 && index == 0 ? (targets[0] / targets[1]) * 100 : 100

        return {
            left: left + '%',
            opacity: targets.length <= 1 && index == 1 ? 0 : 1,
        }
    }
})

const getspintype = (index) => {
    const normalImg = spintype[index].img
    const activeImg = spintype[index].img_act
    const background = index === sindex.value ? activeImg : normalImg
    // let background = index === sindex.value ? spintype[index].img_act : spintype[index].img
    return {
        background: `url(${background}) no-repeat center center/cover`,
        backgroundColor: index === sindex.value ? spintype[index].color : 'rgb(41 45 46)',
        backgroundSize: '80% 80%',
    }
}

const getSpinImage = (index) => {
    const img = file['../../assets/img/luckyspin/spin' + index + '.png'] as { default: string } | undefined
    return img?.default
}

const spintypeislock = (index) => {
    return !rotations.value[index]?.unlock
}

const showdot = (index: number) => {
    return rotations.value[index]?.times > 0
}

const spininfo = reactive({
    sindex: sindex,
    title: spintitle,
    maxwin: maxwin,
})

provide('spininfo', spininfo)

const canSpin = computed(() => {
    const unlock = rotations.value[sindex.value]?.unlock
    const times = rotations.value[sindex.value]?.times
    return unlock && times > 0
})

const handleDeposit = () => {
    router.push({
        path: '/wallets',
        query: {
            back: 2,
        },
    })
}

const handleSpin = async () => {
    if (isRotating.value) return
    if (!canSpin.value) {
        isShaking.value = true
        isTextRed.value = true
        setTimeout(() => {
            isShaking.value = false
            isTextRed.value = false
        }, 1500)
        return
    }
    const res = await store.ReceiveRewards(
        {
            id: 'totalrechargetable',
            content: {
                index: sindex.value,
            },
        },
        false
    )
    if (res.code === 200) {
        const awards = res.awards
        isRotating.value = true
        startspinindex.value = awards.index
        const reward = awards.reward
        const name = reward.id === 'spin' ? 'Spin * ' : 'Cash '
        const mod = file['../../assets/img/luckyspin/spin' + sindex.value + '.png'] as { default: string } | undefined
        signawardinfo.value.items = {
            img: reward.id === 'spin' ? mod?.default : GoldPic,
            name: name,
            worth: reward.num,
        }
    }
}

const handleClose = () => {
    show1.value = false
    popupManager.closeCurrentPopup()
}

const handleHelp = () => {
    ruleshow.value = true
}

const handleSpinType = (index: number) => {
    if (isRotating.value) return
    sindex.value = index
}

const goPage = () => {
    sindex.value = 0
    for (let i = 0; i < rotations.value.length; i++) {
        const item = rotations.value[i]
        if (item.times > 0) {
            sindex.value = i
            break
        }
        if (
            (item.progress < item.targets[item.targets.length - 1] && item.progress > item.targets[0] && item.targets.length > 1) ||
            (item.progress < item.targets[0] && item.targets.length === 1 && total.value > item.targets[0])
        ) {
            sindex.value = i
        }
    }
}

watch(show1, () => {
    console.log('show1--', show1.value)

    if (show1.value) {
        eventBus.off('deposit-spin-end', spinstop)
        eventBus.on('deposit-spin-end', spinstop)
        getEvent()
        goPage()
    } else {
        eventBus.off('deposit-spin-end', spinstop)
    }
})

onMounted(() => {
    console.log('show2--', show1.value)
    sindex.value = 0
    getEvent(true)
})

watch(
    () => wallet?.value.gold,
    () => {
        getEvent(true)
    }
)

const getEvent = async (notclose: boolean = false) => {
    try {
        const res = await store.getSignEvent({ id: 'totalrechargetable' }, false, false)
        console.log('totalrechargetable   res', res)

        if (res.code === 200 && res.acticity.status === 1) {
            rotations.value = res.acticity.rotations
            total.value = res.acticity.total
            const spintimes = getSpinTimes()
            eventBus.emit('totalrechargetable', { show: true, spintimes })

            if (props.type === 1 && !show1.value) {
                show1.value = true
            }
        } else {
            if (notclose) {
                show1.value = false
            } else {
                handleClose()
            }

            eventBus.emit('totalrechargetable', { show: false })
        }
    } catch (e) {
        console.log(e)
        handleClose()
        eventBus.emit('totalrechargetable', { show: false })
    }
}

const getSpinTimes = () => {
    //获取所有次数
    let times = 0
    for (let i = 0; i < rotations.value.length; i++) {
        times += rotations.value[i].times
    }

    return times
}

const spinstop = () => {
    startspinindex.value = -1
    isRotating.value = false
    signawardinfo.value.isshow = true
    getEvent()
}

onBeforeMount(() => {
    eventBus.on('deposit-spin-end', spinstop)
})

onBeforeUnmount(() => {
    eventBus.off('deposit-spin-end', spinstop)
})
</script>
<style lang="scss" scoped>
.channel-pop {
    background-color: #2a2d3d;
    font-family: MicrosoftYaHei;
    color: #ffffff;
}

.wallet-bonus {
    width: 750px;
    min-height: 1300px;
    padding: 0 22px 58px;
    background-color: #232626;
    font-family: MicrosoftYaHei;
    color: #fff;

    .title {
        position: relative;
        @apply flex justify-center items-center;
        padding-top: 33px;
        padding-bottom: 44px;
        position: relative;
        font-size: 26px;
        font-weight: normal;
        align-items: center;

        .help_btn {
            position: absolute;
            left: 15px;
            width: 35px;
            height: 35px;
            background: url('@/assets/img/wallets/qestion_icon.png') no-repeat center center;
            background-size: 35px 35px;
            opacity: 0.7;
        }
        // &::before {
        //     display: inline-block;
        //     content: '';
        //     margin-right: 20px;

        //     width: 52px;
        //     height: 52px;
        //     background: url('@/assets/img/new-home/wallet-icon.png') no-repeat;
        //     background-size: 100% 100%;
        //     position: relative;
        //     top: 10px;
        // }
        .close {
            position: absolute;
            right: 0px;
            width: 30px;
            height: 30px;
            background: url('@/assets/img/close.png') no-repeat center center;
            background-size: 30px 30px;
            align-items: center;
        }
    }
}

.spin-type-info {
    @apply flex;
    width: 100%;
    height: 150px;
    justify-content: space-between;
    .spintype {
        display: flex;
        width: 420px;
        height: 120px;
        background-color: rgb(50 55 56);
        border-radius: 15px;
        padding: 8px;
        align-items: center;
        text-align: center;
        justify-content: center;
        gap: 10px;

        .spintype-item {
            position: relative;
            border-radius: 15px;
            width: 100px;
            height: 100px;
            border-spacing: 30px;
            border: 20px;
            background-color: #0e8e66;

            .spintype-item-icon {
                width: 100px;
                height: 100px;
            }

            .spintype-item-lock {
                position: absolute;
                top: 5px;
                right: 5px;
                width: 26px;
                height: 33px;
                background-image: url('@/assets/img/luckyspin/spinlock.png');
                background-size: 100% 100%;
                &.active {
                    background-image: url('@/assets/img/luckyspin/spinlock_act.png');
                }
            }
            .spintype-item-badge {
                // --van-badge-size: 20px;
            }
        }
    }

    .deposit-tip {
        width: 280px;
        height: 120px;
        background-color: #894825;
        border-radius: 15px;
        padding: 18px;
        font-size: 40px;
        line-height: 100px;
        text-align: center;
        justify-content: center;
        .info-title {
            @apply flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 45px;
            background-image: url('@/assets/img/luckyspin/spininfobg1.png');
            background-size: 100% 100%;
            color: rgb(189, 95, 22);
            font-size: 25px;
            :deep(span) {
                margin-left: 10px;
                color: rgb(237, 83, 0);
            }
        }
        .info-content {
            margin-top: 6px;
            width: 100%;
            font-size: 20px;
            line-height: 40px;
            font-weight: bold;
            :deep(span) {
                color: #ffee6e;
            }
        }
    }
}
.spin-btn {
    @apply flex justify-center items-center;
    margin: 110px auto 0;
    width: 440px;
    height: 88px;
    line-height: 88px;

    .spin-btn-item {
        border: none;
        width: 422px;
        height: 88px;
        // background: linear-gradient(90deg, #2aee88 0%, #9ae871 100%), linear-gradient(#8b0500, #8b0500);
        border-radius: 58px;
        font-size: 30px;
        color: #000000;
        font-weight: 460;
        &.disabled {
            background: #808285;
            cursor: not-allowed;
        }

        &.shake {
            animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
        }

        &.text-red {
            color: red;
        }

        :deep(span) {
            .text-red {
                color: rgb(57, 241, 70);
            }
        }
    }

    @keyframes shake {
        0%,
        100% {
            transform: translateX(0);
        }
        10%,
        30%,
        50%,
        70%,
        90% {
            transform: translateX(-15px);
        }
        20%,
        40%,
        60%,
        80% {
            transform: translateX(15px);
        }
    }
}
.card-bar {
    @apply flex items-center;
    width: 100%;
    height: 60px;

    .deposit-btn {
        @apply flex items-center;
        height: 50px;
        width: 170px;
        color: #000;
        font-size: 20px;
        margin-left: 40px;
        justify-content: center;
        background-image: linear-gradient(90deg, #24ee89 0%, #9fe871 100%), linear-gradient(#e59e20, #e59e20);
        background-blend-mode: normal, normal;
        border-radius: 40px;
        text-align: center;
        align-items: center;
        font-weight: 800;

        :deep(img) {
            width: 35px;
            height: 35px;
            margin-right: 10px;
        }
    }

    .bar-wrap {
        // flex: 1;
        width: 500px;
        position: relative;
        font-size: 20px;
        :deep(.bar) {
            height: 29px;
            border: 3px solid #34ed86;
            background: none;
            .bar-inner {
                background: #60fc24;
                box-shadow: inset 0 0 5px 3px rgba(255, 255, 255, 0.5);
                max-width: 100%;
            }
            .bar-num {
                opacity: 0;
            }
        }
        .bar-content {
            width: 65px;
            height: 65px;
            position: absolute;
            top: 50%;
            transform: translate3d(-50%, -50%, 0);
            // left: 100%;
            text-align: center;
        }
        .pro-text {
            position: absolute;
            display: inline-block;
            background-color: rgb(255, 203, 9);
            transform: translate3d(-50%, -50%, 0);
            padding: 0px 10px;
            border-radius: 30px;
            color: #000;
            font-weight: 700;
            text-align: center;
        }
    }
}
</style>
