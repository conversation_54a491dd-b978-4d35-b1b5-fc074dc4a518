<template>
    <ComPageHeader title="wallet_Withdrawal" :type="1" @close="handleBack" />
    <div class="withdraw">
        <walletHead title="Withdrawal" :type="2" :balance="accountInfo?.balance" />
        <div class="wallet-content">
            <div class="wallet-scroll">
                <div class="payout-method">
                    <MethodIcon />
                    <div v-if="isBinded" class="method-card" @click="editBindInfo">
                        <div class="method-card-content">
                            <div class="binded-item" v-for="item in bindedConfig" :key="item.type">
                                <div class="bind-name">{{ $t(item.text) }}:</div>
                                <div class="bind-text">
                                    {{ getShow(item) }}
                                    <!-- <span :class="['icon', { active: !isSecret }]" v-if="item.type === 1" @click="isSecret = !isSecret"></span> -->
                                </div>
                            </div>
                        </div>

                        <div class="edit-btn" @click="editBindInfo"></div>
                    </div>
                    <div v-else class="bind-account" @click="handleBind">
                        <div>{{ $t('Bind_bank_account') }}</div>
                    </div>
                </div>
                <p class="withdraw-title">{{ $t('Wallet_Withdraw_Amount') }}</p>
                <div class="slect-scroll">
                    <ul class="select-list">
                        <li
                            :class="['list-item amount-num', { active: accountIndex === index, disabled: accountInfo?.balance < item.cny }]"
                            v-for="(item, index) in payOutList"
                            :key="item.id"
                            @click="changeAccount(item, index)"
                        >
                            {{ formatLocalMoney(item.cny, 0) }}
                            <div v-if="item.giftpercentage" class="give-tip">
                                <img
                                    v-for="itd in item.giftpercentage.split('')"
                                    :key="itd"
                                    :src="`//betfugu.com/static/img/playtok/wallet/number/${itd === '%' ? 'percent' : itd}.png`"
                                />
                            </div>
                        </li>
                    </ul>
                    <div class="limit-wrap">
                        <div class="limit-tip">
                            <span>{{ $t('Available_withdrawal') }} <a class="question" @click="handleShow(1)"></a></span>
                            <span :style="{ color: '#ff9800' }"
                                >{{ store.cureencySymbol() }}
                                {{ needFormat ? formatNumber(accountInfo?.balance || 0) : formatNormalNumber(accountInfo?.balance || 0) }}</span
                            >
                        </div>
                        <div class="limit-tip">
                            <span>{{ $t('withdraw_limitation_info') }} <a class="question" @click="handleShow(3)"></a></span>
                            <span>{{ store.cureencySymbol() }} {{ accountInfo?.balanceTimesInfo?.limit || 0 }}</span>
                        </div>
                        <div class="limit-tip">
                            <span>{{ $t('wallet_bet_amount_left') }}<a class="question" @click="handleShow(2)"></a></span>
                            <span>{{ accountInfo?.remainBet || 0 }}</span>
                        </div>
                        <div class="limit-tip">
                            <span>{{ $t('wallet_commission') }}</span>
                            <span :style="{ color: '#ff9800' }">{{ accountInfo?.balanceTimesInfo?.noFeeTimes || 0 > 0 ? 'Free' : payRate }}</span>
                        </div>
                    </div>

                    <div v-if="accountInfo?.balanceTimesInfo?.noFeeTimes || 0 > 0" class="limit-tip free">
                        <span></span>
                        <span :style="{ color: '#747a7b' }">{{ accountInfo?.balanceTimesInfo?.noFeeTimes || 0 }} times left</span>
                    </div>
                    <div class="tip" v-html="$t('wallet_withd_fee_info')"></div>
                </div>
            </div>
            <!-- <div v-if="payRate" class="relative">
                <div
                    class="check-tip"
                    v-html="
                        $t(`wallet_withd_fee_info_${store.wallet?.currency}`, {
                            count: `<span class='text-red-600'>${store.wallet?.currency} ${payRate}</span>`,
                            count1: `<span class='text-red-600'>${store.wallet?.currency} ${payNum}</span>`,
                        })
                    "
                ></div>
            </div> -->
            <div
                :class="[
                    'wallet-btn',
                    {
                        disabled: accountIndex === -1 || accountInfo.balanceTimesInfo.times == 0,
                    },
                ]"
            >
                <div class="btn" @click="doSubmit">
                    {{
                        $t(
                            'withdraw_button',
                            accountIndex !== -1
                                ? {
                                      current_type: store.cureencySymbol(),
                                      current_number: payOutList[accountIndex].cny,
                                  }
                                : {}
                        )
                    }}
                </div>
            </div>
        </div>
        <BindDialog v-model="showBindPhone" :type="2" :tip="$t('Withdraw_must_bind_phone')" />
        <Lamp v-if="cantDepose" :channel="channel" tip="wallet_Withdraw_roll" />
        <!-- <BaseFooter :initTab="3" /> -->
        <WithDdawShare v-model="showShare" :num="shareCount" />
        <Server />
        <AccountBind ref="bindCom" v-model="bindVisible" @confirm="bindConfirm" />
        <BTip
            v-model="showtip"
            :title="$t('')"
            :content="
                $t(tipType === 1 ? 'Withdrawal_balance_info_PHP' : tipType === 2 ? 'wallet_bet_amount_left_info_PHP' : 'wallet_bet_withdrawal_limit')
            "
        />
    </div>
</template>
<script setup lang="ts">
import walletHead from '@/components/wallet/walletHeader.vue'
import BaseFooter from '@/components/BaseFooter.vue'
import BindDialog from '@/components/dialog/BindDialog.vue'
import { AccountInfo } from '@/api/wallet/type'
import * as api from '@/api/wallet'
import { useBaseStore } from '@/stores'
import { useI18n } from 'vue-i18n'
import { formatLocalMoney, formatNumber, formatNormalNumber } from '@/utils/toolsValidate'
import { Log } from '@/api/log'
import Server from '@/components/server.vue'
import Lamp from '@/components/wallet/walletLamp.vue'
import AccountBind from '@/components/wallet/accountBind.vue'
import MethodIcon from '@/components/wallet/methodIcon.vue'
import { Conditon } from '@/api/wallet/type'
import { FORMATE_CURRENCY } from '@/enums'
import BTip from '@/components/wallet/btip.vue'
import ComPageHeader from '@/components/common/ComPageHeader.vue'
const router = useRouter()
const store = useBaseStore()
const i18n = useI18n()

const accountInfo = ref<AccountInfo>()
const payOutList = ref([])
const accountIndex = ref(-1)
const bindVisible = ref(false)
const bindCom = ref(null)
const conditon = ref<Conditon>()

const showBindPhone = ref(false)
const showShare = ref(false)
const isSecret = ref(false)
const shareCount = ref(0)
const cantDepose = ref(false)
const channel = ref([])
const showtip = ref(false)
const tipType = ref(1)

const lampChannel = {
    PHP: 'GCASH',
    // USD: 'USDT',
    USD: 'CashApp',
    TRP: 'Papara',
    BLR: 'Pix',
    NGN: 'Palmpay',
}

const payTypeMap = {
    wakapaypapara1: 'Papara',
    pageasy1: 'Pix',
    ngnwakapay1: 'Palmpay',
}
const needFormat = computed(() => {
    return FORMATE_CURRENCY.includes(store.wallet.currency)
})

const payRate = computed(() => {
    if (!conditon.value || accountIndex.value === -1) {
        return 0
    }
    return Number(
        (Number(conditon.value.feepercent || 0) * Number(payOutList.value[accountIndex.value].cny) + Number(conditon.value.fee || 0)).toFixed(4)
    )
})
const payNum = computed(() => {
    return accountIndex.value !== -1 ? payOutList.value[accountIndex.value].cny - payRate.value : 0
})
const isBinded = computed(() => {
    if (!accountInfo.value?.content?.paytype || !accountInfo.value?.content || accountInfo.value?.content?.paytype === 'usdt') {
        return false
    }
    return !!accountInfo.value?.content[accountInfo.value?.content?.paytype]
})

const bindedConfig = computed(() => {
    switch (store.wallet?.currency) {
        case 'USD':
            return [
                // {
                //     text: 'USDT_address',
                //     type: 1,
                //     key: 'phone',
                // },
                {
                    text: 'name',
                    type: 1,
                    key: 'bankNo',
                },
                // {
                //     text: 'USDT_Dig_curr_channel_name',
                //     type: 2,
                //     key: 'paytype',
                // },
            ]
        case 'NGN':
            return [
                {
                    text: 'Account_Name',
                    type: 1,
                    key: 'bankNo',
                },
                // {
                //     text: 'Channel',
                //     type: 2,
                //     key: 'paytype',
                // },
            ]
            return
        case 'BRL':
            return [
                {
                    text: 'Reg_Mob_phone_No',
                    type: 1,
                    key: 'phone',
                },
                // {
                //     text: 'Channel',
                //     type: 2,
                //     key: 'paytype',
                // },
            ]
        case 'TRY':
            return [
                {
                    text: 'Account_Name',
                    type: 1,
                    key: 'bankNo',
                },
                // {
                //     text: 'Channel',
                //     type: 2,
                //     key: 'paytype',
                // },
            ]
        case 'INR':
            return [
                {
                    text: 'Acc No',
                    type: 1,
                    key: 'phone',
                },
                {
                    text: 'IFSC',
                    type: 2,
                    key: 'ifsc',
                },
            ]
        case 'PHP':
        default:
            return [
                {
                    text: 'Account_Name',
                    type: 1,
                    key: 'phone',
                },
                // {
                //     text: 'Channel',
                //     type: 2,
                //     key: 'paytype',
                // },
            ]
    }
})

watch([() => store.wallet?.balance, () => store.wallet.currency], (val) => {
    val && getAccountInfo(false)
    val && getPayoutstore()
    val && getVipInfo()
})
watch(
    () => store.wallet?.currency,
    () => {
        accountIndex.value = -1
    }
)

const handleBack = () => {
    router.back()
}
const handleShow = (type) => {
    showtip.value = true
    tipType.value = type
}
const getShow = (item) => {
    const key = item.key
    const content = accountInfo.value?.content
    const payTypeValue = content[content.paytype][key]
    return item.key === 'paytype'
        ? (payTypeMap[payTypeValue] || payTypeValue)?.toUpperCase()
        : payTypeValue?.replace(/.*/g, ($1) => {
              return isSecret.value ? '*'.repeat($1.length) : $1
          })
}

const getAccountInfo = async (loading = true) => {
    try {
        const res = await api.getAccountInfo(loading)
        if (res.code === 200) {
            accountInfo.value = res.data
            bindCom.value.resetForm()
        }
    } catch (e) {
        console.log(e)
    }
}

const getPayoutstore = async () => {
    try {
        const res = await api.getPayoutstore(store.userInfo.uid)
        if (res.code === 200) {
            payOutList.value = res.items
            cantDepose.value = /*!res.isAuto || */ !res.channels.length // 去掉isAuto的判断 lzj
            conditon.value = res.conditon
            channel.value = [
                {
                    channel: lampChannel[store.wallet?.currency],
                },
            ]
        }
    } catch (e) {
        console.log(e)
    }
}
const changeAccount = (item, index) => {
    // 新加 具体等接口
    if (store.vipInfo.curLevel < item.vip) {
        return showToast(i18n.t('withdraw_unlock_notif'))
    }
    if (accountIndex.value === index) {
        accountIndex.value = -1
        return
    }
    Log({ event: `wallet_withdraw_${store.wallet.currency}_${item.cny}` })
    accountIndex.value = index
}
// 修改账户
const editBindInfo = () => {
    bindCom.value.editAssign(accountInfo.value.content[accountInfo.value.content.paytype])
    bindVisible.value = true
    Log({ event: `wallet_withdraw_${store.wallet.currency}_bindreceive_edit` })
}
const handleBind = () => {
    bindVisible.value = true
    Log({ event: `wallet_withdraw_${store.wallet.currency}_bindreceive` })
}

const doSubmit = async () => {
    if (!isBinded.value) {
        bindVisible.value = true
        return
    }
    if (accountIndex.value === -1) {
        return
    }
    // if (accountInfo.value.bind && !store.userInfo?.telephone) {
    //     return (showBindPhone.value = true)
    // }

    const { state, times, limit = 0 } = accountInfo.value.balanceTimesInfo

    // 下注流水不足
    if (accountInfo.value?.remainBet) {
        return showToast(
            i18n.t('wallet_withdraw_info1', {
                number: accountInfo.value?.remainBet,
            })
        )
    }
    if (payOutList.value[accountIndex.value].cny > limit) {
        return showToast(i18n.t('wallet_withdraw_info2'))
    }
    if (!state || times === 0) {
        return showToast(i18n.t('Withdrawal_times_max_info', { count: times }))
    }
    const checkItem = payOutList.value[accountIndex.value]
    Log({ event: 'wallet_withdraw_submit' })
    const res = await store.payOut({
        nickname: store.userInfo.nickname,
        gold: checkItem.cny,
        paytype: accountInfo.value.content.paytype,
    })
    if (res.code === 200) {
        showDialog({
            title: i18n.t('Application_submit'),
            message: i18n.t('Withdrawal_application_wait_CS'),
            className: 'common-dialog',
            showCancelButton: true,
            confirmButtonText: 'confirm',
            cancelButtonText: 'cancel',
        })
            .then(() => {
                accountIndex.value = -1
                showShare.value = true
                shareCount.value = checkItem.cny
            })
            .catch((e) => {
                console.log(e, 'Cancel')
            })
        getAccountInfo(false)
    }
}
const bindConfirm = () => {
    getAccountInfo(false)
    bindVisible.value = false
}
const getVipInfo = async (loading = true) => {
    try {
        const info = await store.getVipInfo(loading)
        if (info.code === 200) {
            store.setvipInfo(info)
        }
    } catch (e) {
        console.log(e)
    }
}

onActivated(() => {
    getAccountInfo()
    getPayoutstore()
    getVipInfo()
})
</script>
<style lang="scss" scoped>
@use '@/components/wallet/newCom.scss' as *;

.withdraw {
    height: calc(var(--vh, 1vh) * 100 - var(--home-header));
    font-family: MicrosoftYaHei;
    overflow-y: auto;
    padding: 20px;

    .wallet-content {
        padding: 20px 25px 40px;
        .wallet-scroll {
            @apply flex flex-col;
            padding: 0;
            // height: calc(100% - $btnH);
            overflow-y: inherit;

            .payout-method {
                padding-bottom: 35px;
            }
            .slect-scroll {
                position: relative;
                padding: 0 2px;
                flex: 1;
                overflow-y: scroll;
            }
        }
    }

    .payout-method {
        padding: 30px 0;
        .method-card {
            position: relative;
            margin: 20px auto 0;
            width: 100%;
            padding: 0 70px 0 20px;
            border-radius: 10px;
            background: linear-gradient(90deg, #027757 0, #00953a);
            display: flex;
            flex-direction: column;
            .method-card-content {
                display: flex;
                align-items: center;
                justify-content: space-between;
                min-height: 88px;
                .binded-item {
                    display: flex;
                    font-size: 22px;
                    font-weight: bold;
                    color: #fefefebb;
                    .bind-name {
                        margin-right: 10px;
                        // min-width: 250px;
                    }
                    .bind-text {
                        word-break: break-all;

                        .icon {
                            display: inline-block;
                            // width: 19px;
                            // height: 19px;

                            &::after {
                                display: inline-block;
                                content: '';
                                width: 36px;
                                height: 24px;
                                background: url('@/assets/img/wallets/hiden.png') no-repeat;
                                background-size: 100% 100%;
                            }
                            &.active {
                                &::after {
                                    background-image: url('@/assets/img/wallets/visible.png');
                                }
                            }
                        }
                    }
                }
            }

            .edit-btn {
                position: absolute;
                width: 70px;
                height: 70px;
                transform: translate(0, -50%);
                right: 0%;
                top: 50%;
                &::after {
                    display: inline-block;
                    content: '';
                    width: 36px;
                    height: 36px;
                    background: url('@/assets/img/wallets/edit.png') no-repeat;
                    background-size: 100% 100%;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
            }
            // .secret-btn {
            //     display: flex;
            //     justify-content: flex-end;
            //     align-items: flex-end;
            //     position: absolute;
            //     right: 10px;
            //     top: 10px;
            //     width: 40px;
            //     height: 40px;

            //     &::after {
            //         display: inline-block;
            //         content: '';
            //         width: 36px;
            //         height: 24px;
            //         background: url('@/assets/img/wallets/hiden.png') no-repeat;
            //         background-size: 100% 100%;
            //     }
            //     &.active {
            //         &::after {
            //             background-image: url('@/assets/img/wallets/visible.png');
            //         }
            //     }
            // }
        }
    }
    .withdraw-title {
        padding: 0 5px;
        margin-bottom: 34px;
        font-size: 30px;
        line-height: 30px;
        letter-spacing: -1px;
        color: #ffffff;
    }
    .tip {
        margin-top: 6px;
        padding-bottom: 30px;
        font-size: 22px;
        font-weight: bold;
        line-height: 30px;
        color: #668292;
    }
    .limit-wrap {
        margin-top: 30px;
    }
    .limit-tip {
        @apply flex justify-between;
        color: #fff;
        font-size: 26px;

        font-weight: bold;
        align-items: center;

        span {
            align-items: center;
            line-height: 65px;
            &:first-child {
                flex: 1;
            }
            &:last-child {
                // color: rgb(255, 152, 32);
            }
        }

        &.free {
            color: #747a7b;
            margin-top: -28px;
            font-size: 20px;
        }
        .question {
            position: relative;
            top: 8px;
            display: inline-block;
            margin-left: 5px;
            width: 36px;
            height: 36px;
            background: url('@/assets/img/icon/com_tip_gan.svg') no-repeat center center;
            filter: invert(70%) sepia(94%) saturate(1500%) hue-rotate(345deg);
            background-size: 100% 100%;
        }
    }
    .bind-account {
        text-align: center;
        margin-top: 20px;
        div {
            margin: 0 auto;
            display: inline-block;
            position: relative;
            font-size: 36px;
            color: #ffea77;
            text-align: center;

            &::before,
            &::after {
                display: inline-block;
                content: '';
            }
            &::after {
                position: relative;
                top: 3px;
                margin-left: 7px;
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background: url('@/assets/img/wallets/y-row.png') no-repeat;
                background-size: 100% 100%;
            }
            &::before {
                flex-grow: 0;
                position: absolute;
                left: 0;
                bottom: 0;
                width: 100%;
                height: 5px;
                background-color: #ffea77;
                border-radius: 2px;
            }
        }
    }

    .check-tip {
        position: absolute;
        bottom: -10px;
        min-height: 120px;
        padding: 27px 72px;
        font-size: 24px;
        line-height: 40px;
        color: #fefeff;
        background: url('@/assets/img/wallets/rate_tip.png') no-repeat;
        background-size: 100% 100%;

        :deep(.text-red-600) {
            color: #ffea77;
        }
    }
    .list-item.disabled {
        pointer-events: none;
        color: rgba(160, 160, 160, 0.4);
    }
    .wallet-btn.disabled {
        pointer-events: none;
        opacity: 0.7;
    }
}
</style>
