/* 初始化样式
------------------------------- */
@font-face {
  font-family: 'Helvet';
  src: url('../assets/font/HelveticaLTStd-BlkCond.woff2') format('woff2');
  font-display: swap;
  /* 优化字体加载 */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  outline: none !important;
  user-select: none;
  overscroll-behavior: none !important;
}

:root {
  --main-bg: #232626;
  --main-text-color: #7e776e;
  --main-text-font: MicrosoftYaHei;

  // --header-height: 100px;
  --header-height: max(calc(72px + constant(safe-area-inset-top)), 100px) ;
  --header-height: max(calc(72px + env(safe-area-inset-top)), 100px);

  --home-header: calc(92px + constant(safe-area-inset-top));
  --home-header: calc(92px + env(safe-area-inset-top));

  --menu-header: max(110px,calc(72px + constant(safe-area-inset-top)));
  --menu-header: max(110px,calc(72px + env(safe-area-inset-top)));

  // 钱包头部高度
  --wallet-head-height: 180px;
  --safe-height: 0px;
  --page-padding: 25px;
  --footer-line: 2px;
  --footer-height: 160px;
  --footer-color: #232626;
  --primary-btn-color: rgb(252, 47, 86);
  --second-btn-color: rgb(58, 58, 70);
  --indicator-height: 43px;

  --font-family: MicrosoftYaHei;
  --lamp-height: 60px;
  --banner-height: 330px;
  --van-overlay-background: rgba(0, 0, 0, 0.8);
}

.hide-banner {
  --banner-height: 20px;
}

::-webkit-scrollbar {
  display: none;
}

html,
body,
#app {
  width: 100%;
  height: 100vh;
  background: var(--main-bg) !important;
  margin: 0;
  padding: 0;
  overflow: hidden;
  // touch-action: none;
  color: var(--main-text-color);
  font-family: var(--main-text-font);

}

#app {
  height: calc(var(--vh, 1vh) * 100);
}

.van-popup.common-dialog {
  width: 668px;
  min-height: 400px;
  padding: 75px 49px 20px;
  background-color: #2a2d3d;
  border-radius: 40px;
  // border: 2px solid #544f4c;
  font-size: 36px;
  font-weight: bold;
  line-height: 48px;
  color: #fff;
  z-index: 3200 !important;

  .van-dialog__header {
    text-align: center;
    font-size: 40px;
    margin-bottom: 10px;
    color: #fff;

    .close {
      position: absolute;
      top: 30px;
      right: 30px;
      width: 33px;
      height: 34px;
      background: url('@/assets/img/new-home/close.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  .van-dialog__message {
    padding: 0;
    min-height: 100px;
    margin-bottom: 30px;
    color: #fff;
    font-size: 36px;
  }

  .van-dialog__footer {
    @apply flex justify-center;

    &::before,
    &::after {
      display: none;
    }

    .van-dialog__confirm,
    .van-dialog__cancel {
      flex: 0 0 220px;
      height: 88px;
      border-radius: 44px;
      font-size: 30px;
      color: #fff;
    }

    .van-dialog__confirm {
      background-image: linear-gradient(90deg, #10c57f 0%, #0e8e66 100%), linear-gradient(#e59e20, #e59e20);
    }

    .van-dialog__cancel {
      margin-right: 80px;
      background-image: linear-gradient(90deg, #369ae0 0%, #227fbd 40%, #0e6399 100%), linear-gradient(#e59e20, #e59e20);
    }
  }

  .van-hairline--left:after {
    display: none;
  }

  .text-orange {
    color: #ffa72a;
  }

  &.video-lock {
    .van-dialog__message {
      overflow-y: inherit;
      max-height: inherit;
      line-height: 25px;
      font-size: 32px;
      text-align: left !important;
    }
  }
}

.wallets-head {
  display: flex;
  padding-top: 13px;
  height: var(--wallet-head-height);

  .head-content {
    display: flex;
    flex-direction: column;
    // gap: 16px;
    justify-content: center;
    align-items: center;
    flex: 1;
    font-size: 48px;
    font-weight: bold;
    line-height: 30px;
    color: #ffffff;

    .head-num {
      margin-top: 16px;
    }

    .head-coin {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 6px;
      width: 220px;
      height: 60px;
      background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.2));
      border-radius: 30px;
      border: 4px solid #ffffff;
      font-size: 36px;

      .icon {
        display: inline-block;
        width: 30px;
        height: 30px;
        background: url('@/assets/img/wallets/slect_arrow.png') no-repeat;
        background-size: 100% 100%;

        &.bonus-icon {
          background-image: url('@/assets/img/wallets/help_icon.png');
        }
      }
    }

    &.right-line {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        width: 4px;
        height: 140px;
        background-color: rgba(255, 255, 255, 0.4);
        border-radius: 2px;
        transform: translateY(-50%);
      }
    }
  }
}

.empty-tip {
  font-size: 25px;
}

.elipsis {
  white-space: nowrap;
  /* 保持文本在一行显示 */
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
}

.van-toast--success,
.van-toast--fail {
  max-width: inherit !important;
  width: 680px !important;
  padding: 0 !important;
  min-height: 100px;
  background: none !important;
  z-index: 3005 !important;

  .van-toast__icon {
    display: none;
  }

  .van-toast__text {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 680px;
    min-height: 100px;
    background: url('@/assets/img/succes.png') no-repeat;
    background-size: 100% 100%;
  }
}

.van-toast--fail {
  .van-toast__text {
    background-image: url('@/assets/img/error.png');

    &::before {
      content: '';
      display: inline-block;
      margin-right: 15px;
      width: 36px;
      height: 32px;
      background: url('@/assets/img/error_icon.png') no-repeat;
      background-size: 100% 100%;
    }
  }
}

// 全局修复 ActionSheet 底部安全区域的白色问题
.van-action-sheet {
  background-color: #2c3031 !important;
}