<template>
    <div class="home-main">
        <van-tabs
            :class="{ 'first-tab': activeIdx === 0 }"
            v-model:active="activeIdx"
            animated
            :swipeable="activeIdx !== 0"
            @click-tab="changeTab"
            @change="tabChange"
        >
            <van-tab v-for="item in tabData" :title="$t(item.name)" :key="item.name">
                <template #title>
                    <div class="tab-title">
                        <div class="tab-title_icon"><img v-lazy="item.picture" /></div>
                        <div class="tab-title_name">{{ $t(item.name) }}</div>
                    </div></template
                >
                <div class="tab-content">
                    <ForUList v-if="item.type === 'forU'" />
                    <TabList v-else :type="item.type" />
                </div>
            </van-tab>
        </van-tabs>
    </div>
</template>
<script setup lang="ts" name="homeMain">
import { useBaseStore } from '@/stores'
import { Categorys } from '@/api/home/<USER>'
import ForUList from './forUList.vue'
import TabList from './tabList.vue'
import { Log } from '@/api/log'
import eventBus from '@/utils/bus'
import Foru from '@/assets/img/new-home/foru.png'

const store = useBaseStore()

watch(
    () => store.showDown,
    (val) => {
        if (!val) {
            // getOffseTop()
        }
    }
)

const getOffseTop = (set = false) => {
    const header = document.querySelector('.home-header') as Element
    offsetTop.value = header?.getBoundingClientRect().height || 0
    if (set) {
        const sticky = document.querySelector('.van-sticky--fixed') as Element
        sticky.style.top = offsetTop.value
    }
}

const activeIdx = ref(0)
const offsetTop = ref(0)
const tabData = computed<Categorys[]>(() => {
    console.log(
        'store.menuData',
        store.menuData?.length
            ? [
                  {
                      name: 'Game_type_For_u',
                      picture: Foru,
                      type: 'forU',
                  },
                  ...store.menuData,
              ]
            : []
    )

    return store.menuData?.length
        ? [
              {
                  name: 'Game_type_For_u',
                  picture: Foru,
                  type: 'forU',
              },
              ...store.menuData,
          ]
        : []
})

const changeTab = (item) => {
    Log({
        event: `home_${item.title}`,
    })
    console.log('changeTab', item, tabData.value)
}
const tabChange = () => {
    // document.querySelector('.home-scroll').scrollTop = 0
}
const handleChangeTab = (item) => {
    store.homeShow = true
    activeIdx.value = tabData.value.findIndex((itd) => itd.type === item.type)
}
const handleTrigerMenu = (item) => {
    const index = tabData.value.findIndex((menu) => menu.type === item.type)
    if (~index) {
        activeIdx.value = index
    }
}
const handelResize = () => {
    // getOffseTop()
}

onBeforeMount(() => {
    eventBus.on('changeTab', handleChangeTab)
    eventBus.on('tabmenu-change', handleTrigerMenu)
    eventBus.on('resize', handelResize)
})
onBeforeUnmount(() => {
    eventBus.off('changeTab', handleChangeTab)
    eventBus.off('tabmenu-change', handleTrigerMenu)
    eventBus.off('resize', handelResize)
})
onActivated(() => {
    // setTimeout(() => {
    //     getOffseTop()
    // }, 1000)
})
</script>
<style lang="scss" scoped>
// .home-main {
//     height: 100%;

//     .tab-content {
//         height: 100%;
//         overflow-y: scroll;
//     }
// }
:deep(.van-tabs__nav) {
    padding-left: 29px;
}
:deep(.van-sticky--fixed) {
    width: 750px !important;
    left: 0;
    // padding-left: 15px;
    background: var(--main-bg);
}
:deep(.van-tab__panel-wrapper--inactive[aria-hidden='false']) {
    height: auto;
}
</style>
