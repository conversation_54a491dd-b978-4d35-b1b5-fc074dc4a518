<template>
    <div class="banner" :class="{ active: !store.token && bannerList?.length }">
        <!-- :lazy="true" -->
        <!-- loading="lazy"  -->
        <!-- <img v-if="!bannerList?.length" :src="changeImgHost('https://cach.kaxyx.top/gamecategroyicon/ccbc6dbd-a9a8-47a0-b770-1f5aa8ddba34.png')" /> -->
        <Swiper
            v-if="bannerList?.length"
            :slidesPerView="1"
            :loop="true"
            :pagination="{}"
            :navigation="true"
            :autoplay="{ delay: 3000, disableOnInteraction: false }"
            :modules="modules"
            @swiper="onSwiperInit"
        >
            <swiper-slide v-for="(item, index) in bannerList" :key="index" :style="{ height: bannerList.length > 1 ? '93%' : '100%' }">
                <div class="swiper-item" @click="handleAction(item)">
                    <img :srcset="changeImgHost(item.picture)" />
                    <div class="item-tip" v-html="$t(item.name)"></div>
                </div>
            </swiper-slide>
        </Swiper>
    </div>
</template>
<script setup lang="ts">
import { useBaseStore } from '@/stores'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Pagination, Autoplay, EffectCreative } from 'swiper/modules'
import 'swiper/css'
import 'swiper/scss/pagination'
import 'swiper/scss/effect-creative'
import { checkLogin, changeImgHost } from '@/utils'
import { useRouter } from 'vue-router'
import eventbus from '@/utils/bus'
import { useGame } from '@/hooks/useGame'
import { Log } from '@/api/log'
import { useI18n } from 'vue-i18n'

const modules = [Autoplay, Pagination]

const bodyDom = document.querySelector('body')
const store = useBaseStore()
const router = useRouter()
const { goGamePage } = useGame()
const i18n = useI18n()
const swiperInstance = ref(null) // 存储 Swiper 实例

const onSwiperInit = (swiper) => {
    swiperInstance.value = swiper
    swiper.autoplay.start() // 初始化后立即启动
}

const bannerList = computed(() => {
    console.log(store.gameList.banner, 'store.gameList.banner')

    return store.gameList.banner
})

document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible' && swiperInstance.value) {
        swiperInstance.value.autoplay?.start() // 返回页面时重新启动
    }
})

watch(
    bannerList,
    (val) => {
        if (val.length) {
            bodyDom.classList.remove('hide-banner')
        } else {
            bodyDom.classList.add('hide-banner')
        }
    },
    {
        deep: true,
    }
)

const handleAction = async (item) => {
    Log({
        event: `home_${i18n.t(item.name)}`,
    })
    if (item.jump && item.jump !== 'url') {
        try {
            const logined = await checkLogin()
            if (!logined) {
                return
            }
        } catch (e) {
            console.log(e, 'eee')
            return
        }
    }
    switch (item.jump) {
        case 'page':
            router.push({
                path: item.param,
                query: {
                    back: 1,
                },
            })
            break
        case 'activity':
            eventbus.emit('activity', item)
            break
        case 'game':
            goGamePage({ gametag: item.param })
            break
        case 'url':
            window.open(item.param)
            break
        default:
            break
    }
}

onActivated(() => {
    swiperInstance.value?.autoplay?.start() // 返回页面时重新启动
})
</script>
<style lang="scss" scoped>
.banner {
    // padding: 10px 15px 15px 20px;
    height: var(--banner-height);
    // overflow: visible;
    &.active {
        height: calc(var(--banner-height) + 110px);
    }
    img {
        width: 100%;
        height: 100%;
    }

    .swiper,
    .swiper-wrapper {
        width: 100%;
        height: 100%;

        display: flex;
        align-items: center;
        justify-content: center;
    }
    .swiper-slide {
        width: 100%;
        height: 100%;

        display: flex;
        align-items: center;
        justify-content: center;
    }

    .swiper-item {
        position: relative;
        width: 95%;
        height: 100%;
        transform-style: preserve-3d;
        border-radius: 30px;
        overflow: hidden;

        img {
            width: 100%;
            height: 100%;
            object-fit: fill;
        }
        .item-tip {
            position: absolute;
            top: 26px;
            left: 42px;
            width: 404px;
            height: 135px;
            font-weight: bold;
            font-size: 36px;
            line-height: 48px;
            color: #fff;
        }
    }
    // :deep(.swiper-pagination) {
    //     overflow: visible;
    //     display: flex;
    //     align-items: center;
    //     width: auto;
    //     height: 28px;
    //     left: 50%;
    //     transform: translateX(-50%);
    //     padding: 0 10px;
    //     background: rgba(0, 0, 0, 0.6);
    //     border-radius: 14px;
    //     // bottom: -20px;

    //     .swiper-pagination-bullet {
    //         margin: 0px 15px;
    //         width: 8px;
    //         height: 8px;
    //         background: rgba(255, 255, 255, 1);
    //         // border-radius: 50%;

    //         &.swiper-pagination-bullet-active {
    //             width: 25px;
    //             height: 8px;
    //             background: rgba(255, 255, 255, 0.9);
    //             border-radius: 4px;
    //         }
    //     }
    // }
    :deep(.swiper-pagination) {
        display: flex;
        justify-content: center;
        align-items: center;
        // gap: 2px;
        bottom: 0px;
    }

    /* 分页器项目（圆点/进度条共用样式） */
    :deep(.swiper-pagination-bullet) {
        position: relative;
        width: 11px; /* 默认圆点宽度 */
        height: 11px; /* 默认圆点高度 */
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%; /* 初始为圆形 */
        opacity: 1;
        transition: all 0.3s ease; /* 圆点 ↔ 进度条的过渡动画 */
        overflow: hidden;
        cursor: pointer;

        /* 进度条填充部分（初始隐藏） */
        &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #83e472;
            transform: scaleX(0);
            transform-origin: left;
            border-radius: inherit; /* 继承父元素的圆角 */
            transition: inherit; /* 同步父元素的过渡效果 */
        }
    }

    /* 激活状态：圆点 → 进度条 */
    :deep(.swiper-pagination-bullet-active) {
        width: 80px; /* 进度条宽度 */
        height: 12px; /* 进度条高度 */
        border-radius: 10px; /* 矩形圆角 */

        /* 进度条填充动画（3秒从0%到100%） */
        &::after {
            animation: fillProgress 3s ease forwards;
        }
    }

    /* 进度条填充动画 */
    @keyframes fillProgress {
        from {
            transform: scaleX(0);
        }
        to {
            transform: scaleX(1);
        }
    }
}
.swiper-slide-prev {
    // perspective: ;
    transform: translate(100%, 0, 0);
}
.swiper-slide-next {
    transform: translate(0, 0, -400px);
}
.swiper-slide-active {
    transform: translate(0, 0, 0);
}
</style>
