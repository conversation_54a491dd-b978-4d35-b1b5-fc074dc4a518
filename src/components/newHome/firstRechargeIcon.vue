<template>
    <div v-if="show" class="charge-float" @click="handleClick">
        <svg xmlns="http://www.w3.org/2000/svg" width="90" height="90" viewBox="0 0 90 90" fill="none">
            <circle cx="45" cy="45" r="43.25" fill="#930B4D" stroke="url(#paint0_linear_247_185)" stroke-width="2.5" />
            <defs>
                <linearGradient id="paint0_linear_247_185" x1="45" y1="3" x2="45" y2="87" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#FF398E" />
                    <stop offset="1" stop-color="#B81259" />
                </linearGradient>
            </defs>
        </svg>
        <div class="text" v-html="$t('firstdps_title')"></div>
    </div>
</template>
<script lang="ts" setup>
import eventBus from '@/utils/bus'
import { useBaseStore } from '@/stores'

const show = ref(false)
const store = useBaseStore()

const handleVisible = (flag) => {
    show.value = flag
}
const handleClick = () => {
    eventBus.emit('activity', {
        param: 'firstpayment',
    })
}
const handlePoint = () => {
    store
        .getSignEvent(
            {
                id: 'firstpayment',
            },
            false,
            false
        )
        .then((res) => {
            if (res.code === 200 && res.acticity.status === 1) {
                handleVisible(true)
            } else {
                handleVisible(false)
            }
        })
}

onBeforeMount(() => {
    eventBus.on('chargeGift', handleVisible)
    eventBus.on('checkPoint', handlePoint)
    eventBus.on('paymentFinish', handlePoint)
})
onBeforeUnmount(() => {
    eventBus.off('chargeGift', handleVisible)
    eventBus.on('checkPoint', handlePoint)
    eventBus.on('paymentFinish', handlePoint)
})
</script>
<style lang="scss" scoped>
.charge-float {
    position: absolute;
    bottom: 130px;
    right: 20px;
    width: 93px;
    height: 93px;
    &::after {
        position: absolute;
        top: 50%;
        left: 50%;
        content: '';
        width: 120px;
        height: 120px;
        background: url('@/assets/img/home/<USER>') no-repeat;
        background-size: 100% 100%;
        animation: rotated linear 2s infinite;
    }

    svg {
        width: 100%;
        height: 100%;
    }
    .text {
        position: absolute;
        bottom: 0;
        width: 100%;
        color: #fff;
        text-align: center;
        text-shadow: 1px 2px 2px #390015;
        // -webkit-text-stroke-width: 1px;
        -webkit-text-stroke-color: #390015;
        font-family: 'Helvet';
        // font-family: 'Microsoft YaHei';
        font-size: 28px;
        font-style: normal;
        font-weight: 900;
        line-height: 22px;
    }
    &::before {
        position: absolute;
        left: 50%;
        top: 0px;
        content: '';
        width: 77px;
        height: 81px;
        background: url('@/assets/img/gift.png') no-repeat;
        background-size: 100% 100%;
        transform: translatex(-50%);
    }
}
</style>
