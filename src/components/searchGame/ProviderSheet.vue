<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-30 15:20:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-08-05 14:07:52
 * @FilePath     : /src/components/searchGame/ProviderSheet.vue
 * @Description  : 供应商选择弹窗组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-30 15:20:00
-->

<template>
    <van-action-sheet v-model:show="visible" :class="sheetClass" :duration="0.1">
        <div class="sheet-header">
            <span class="sheet-title">{{ title }}</span>
            <div class="action-buttons">
                <button class="clear-all-btn" @click="handleClearAll">{{ $t('search_game.clear_all') }}</button>
                <van-icon name="cross" class="sheet-close" @click="handleClose" />
            </div>
        </div>
        <div class="provider-list">
            <div
                v-for="provider in providers"
                :key="provider.name"
                class="provider-item"
                :class="{ 'provider-item--selected': isSelected(provider.name) }"
                @click="handleToggleProvider(provider)"
            >
                <div class="left-content">
                    <van-checkbox
                        :model-value="isSelected(provider.name)"
                        shape="square"
                        checked-color="#00d4aa"
                        class="provider-checkbox"
                        @click.stop
                    />
                    <img :src="provider.avator" :alt="provider.name" class="provider-logo" />
                </div>
                <span class="game-count">{{ provider.gameCount || 0 }} Games</span>
            </div>
        </div>
    </van-action-sheet>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Gamefirm } from '@/api/home/<USER>'

// 扩展 Gamefirm 接口以包含游戏数量
interface GamefirmWithCount extends Gamefirm {
    gameCount?: number
}

// 定义属性
interface Props {
    /** 是否显示弹窗 */
    show?: boolean
    /** 弹窗标题 */
    title?: string
    /** 供应商列表 */
    providers?: GamefirmWithCount[]
    /** 当前选中的供应商对象数组 */
    selectedProviders?: GamefirmWithCount[]
    /** 自定义样式类名 */
    customClass?: string
}

// 定义事件
interface Emits {
    /** 更新显示状态 */
    (e: 'update:show', value: boolean): void
    /** 更新选中的供应商 - 传递完整的 provider 对象数组 */
    (e: 'update:selectedProviders', value: Gamefirm[]): void
    /** 选择供应商 */
    (e: 'select', provider: Gamefirm): void
    /** 取消选择供应商 */
    (e: 'unselect', provider: Gamefirm): void
    /** 清空所有选择 */
    (e: 'clear-all'): void
    /** 关闭弹窗 */
    (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
    show: false,
    title: 'Select',
    providers: () => [],
    selectedProviders: () => [],
    customClass: '',
})

const emit = defineEmits<Emits>()

// 计算属性：弹窗显示状态
const visible = computed({
    get: () => props.show,
    set: (value: boolean) => emit('update:show', value),
})

// 计算属性：弹窗样式类名
const sheetClass = computed(() => {
    return `provider-sheet ${props.customClass}`.trim()
})

// 检查供应商是否被选中 - 使用 name 字段作为唯一标识
const isSelected = (providerName: string): boolean => {
    return props.selectedProviders.some((provider) => provider.name === providerName)
}

// 处理供应商选择切换
const handleToggleProvider = (provider: Gamefirm) => {
    const currentSelected = [...props.selectedProviders]
    const index = currentSelected.findIndex((p) => p.name === provider.name)

    let selectedProviders: Gamefirm[] = []

    if (index > -1) {
        // 取消选择
        currentSelected.splice(index, 1)
        emit('unselect', provider)
    } else {
        // 添加选择
        currentSelected.push(provider)
        emit('select', provider)
    }

    selectedProviders = currentSelected
    emit('update:selectedProviders', selectedProviders)
}

// 处理清空所有选择
const handleClearAll = () => {
    emit('update:selectedProviders', [])
    emit('clear-all')
}

// 处理关闭
const handleClose = () => {
    visible.value = false
    emit('close')
}
</script>

<style lang="scss" scoped>
// 弹窗样式
.provider-sheet {
    .sheet-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30px 24px;
        background-color: #2c3031;
        border-radius: 12px 12px 0 0;
        margin-bottom: -1px;
        position: relative;
        z-index: 1;

        .sheet-title {
            font-size: 32px;
            font-weight: 600;
            color: #ffffff;
        }

        .action-buttons {
            display: flex;
            align-items: center;
            gap: 16px;

            font-size: 28px;
            .clear-all-btn {
                color: #ffffff;
                background: transparent;
                padding: 0 16px;
                border-radius: 12px;
                background-color: #404647;
                height: 56px;
                line-height: 56px;
                font-weight: 500;

                &:hover {
                    opacity: 0.8;
                }
            }

            .sheet-close {
                color: #ffffff;
                width: 56px;
                height: 56px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #404647;
                border-radius: 8px;
                font-weight: 500;
                ss &:hover {
                    opacity: 0.8;
                }
            }
        }
    }

    // 供应商列表样式
    .provider-list {
        background-color: #2c3031;
        padding-bottom: 40px;
        margin-top: -1px;

        .provider-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 92px;
            padding: 20px 24px;
            background-color: #2c3031;
            transition: background-color 0.2s ease;

            &.provider-item--selected {
                background-color: #363838;
            }

            .left-content {
                display: flex;
                align-items: center;
                gap: 16px;

                .provider-checkbox {
                    margin-right: 0;

                    // 自定义 Vant Checkbox 样式
                    :deep(.van-checkbox__icon) {
                        width: 28px;
                        height: 28px;
                        border-radius: 6px;
                        border: 1px solid #828d8e;
                        background-color: transparent;
                        // 禁用所有动画和过渡效果
                        transition: none;
                        animation: none;
                        transform: none;
                        // 确保没有额外的外边距或内边距
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                        // 覆盖可能的 Vant 默认样式
                        outline: none;
                        box-shadow: none;

                        .van-icon {
                            border: none;
                            // 禁用图标的动画和过渡效果
                            transition: none;
                            animation: none;
                            transform: none;
                            position: relative;
                        }

                        // 选中状态
                        &.van-checkbox__icon--checked {
                            width: 28px;
                            height: 28px;
                            background-color: #2eee88;
                            border: 1px solid #2eee88;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border-radius: 6px;
                            // 禁用选中状态的动画
                            transition: none;
                            animation: none;
                            transform: none;
                            // 确保没有额外的外边距或内边距
                            margin: 0;
                            padding: 0;
                            box-sizing: border-box;

                            .van-icon {
                                color: #000000;
                                font-size: 20px;
                                line-height: 1;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                // 确保对勾图标始终居中，禁用所有动画
                                position: static;
                                transition: none;
                                animation: none;
                                transform: none;
                                left: auto;
                                top: auto;
                                right: auto;
                                bottom: auto;
                            }
                        }
                    }
                }

                .provider-logo {
                    // max-width: 120px;
                    height: 56px;
                    object-fit: contain;
                    margin-left: 40px;
                }
            }

            .game-count {
                font-size: 28px;
                color: #adb7ba;
                text-align: right;
            }
        }
    }
}
</style>
