<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-30 20:33:48
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-08-05 14:57:39
 * @FilePath     : /src/components/searchGame/SearchHistory.vue
 * @Description  : 搜索历史和推荐游戏组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-30 20:33:48
-->

<template>
    <div v-if="visible" class="search-history">
        <!-- 提示信息 -->
        <div v-if="showSearchTip" class="search-tip">{{ $t('search_game.search_tip_min_chars') }}</div>

        <!-- 历史记录 -->
        <div v-if="localSearchHistory.length > 0" class="history-section">
            <div class="section-header">
                <!-- <h3>History</h3> -->
                <button class="clear-btn" @click.stop="clearAllHistory">{{ $t('search_game.clear_search') }}({{ localSearchHistory.length }})</button>

                <!-- 关闭按钮 -->
                <div class="close-btn-wrapper" @click.stop="handleClose">
                    <van-icon name="cross" class="close-btn" />
                </div>
            </div>
            <div class="history-items">
                <div v-for="(item, index) in localSearchHistory" :key="index" class="history-item">
                    <span class="history-text" @click.stop="selectHistory(item)">{{ item }}</span>
                    <van-icon name="cross" class="delete-icon" @click.stop="removeHistoryItem(index)" />
                </div>
            </div>
        </div>

        <!-- 推荐游戏 -->
        <!-- <div class="recommend-section">
            <div class="section-header">
                <h3>Recommend games</h3>
            </div>
            <div class="game-items">
                <div v-for="(game, index) in recommendGames" :key="index" class="game-item" @click="selectGame(game)">
                    {{ game.name }}
                </div>
            </div>
        </div> -->
    </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, onMounted, watch } from 'vue'

interface Game {
    id: number | string
    name: string
}

interface Props {
    visible?: boolean
    keyword?: string
    searchHistory?: string[] // 仍然保留这个prop以便于向下兼容或初始数据传入
    showTip?: boolean // 强制显示提示
}

interface Emits {
    (e: 'select-history', keyword: string): void
    (e: 'select-game', game: Game): void
    (e: 'update:searchHistory', history: string[]): void
    (e: 'add-history', keyword: string): void
    (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
    visible: false,
    keyword: '',
    searchHistory: () => [],
    showTip: false,
})

const emit = defineEmits<Emits>()

// 推荐游戏列表
const recommendGames = ref<Game[]>([
    { id: 1, name: 'Mines 2' },
    { id: 2, name: 'Fortune Ox' },
    { id: 3, name: 'Aviator' },
    { id: 4, name: '5 Lions' },
    { id: 5, name: 'Starlight Princess 1000' },
    { id: 6, name: 'Gold Party' },
])

// 本地管理的搜索历史
const localSearchHistory = ref<string[]>([])

// 始终显示搜索提示
const showSearchTip = computed(() => {
    // 只要历史面板可见，就显示提示
    return props.visible
})

// 初始化时加载搜索历史
onMounted(() => {
    loadSearchHistory()

    // 如果有传入的历史记录，与本地合并
    if (props.searchHistory && props.searchHistory.length > 0) {
        // 过滤掉重复项
        const merged = [...props.searchHistory]
        for (const item of localSearchHistory.value) {
            if (!merged.includes(item)) {
                merged.unshift(item)
            }
        }

        if (merged.length > 10) {
            localSearchHistory.value = merged.slice(0, 10)
        } else {
            localSearchHistory.value = merged
        }

        saveSearchHistory()
    }
})

// 监听外部searchHistory的变化
watch(
    () => props.searchHistory,
    (newValue) => {
        if (newValue && newValue.length > 0) {
            // 只在组件初始化时同步一次外部历史
            if (localSearchHistory.value.length === 0) {
                localSearchHistory.value = [...newValue]
            }
        }
    }
)

// 加载搜索历史
const loadSearchHistory = () => {
    try {
        const savedHistory = localStorage.getItem('searchHistory')
        if (savedHistory) {
            localSearchHistory.value = JSON.parse(savedHistory)
        }
    } catch (error) {
        console.error('❌ Failed to load search history:', error)
    }
}

// 保存搜索历史
const saveSearchHistory = () => {
    try {
        localStorage.setItem('searchHistory', JSON.stringify(localSearchHistory.value))

        // 向父组件通知历史记录已更新
        emit('update:searchHistory', localSearchHistory.value)
    } catch (error) {
        console.error('❌ Failed to save search history:', error)
    }
}

// 添加搜索历史
const addSearchHistory = (keyword: string) => {
    if (!keyword || keyword.trim() === '') {
        return
    }

    // 如果已存在，先移除旧的
    const index = localSearchHistory.value.indexOf(keyword)
    if (index > -1) {
        localSearchHistory.value.splice(index, 1)
    }

    // 添加到最前面
    localSearchHistory.value.unshift(keyword)

    // 限制历史记录数量
    if (localSearchHistory.value.length > 10) {
        localSearchHistory.value = localSearchHistory.value.slice(0, 10)
    }

    saveSearchHistory()
}

// 选择历史记录
const selectHistory = (keyword: string) => {
    emit('select-history', keyword)
}

// 选择游戏
const selectGame = (game: Game) => {
    emit('select-game', game)
    // 可以选择将游戏名称添加到搜索历史
    addSearchHistory(game.name)
}

// 清除所有历史
const clearAllHistory = () => {
    localSearchHistory.value = []
    saveSearchHistory()
}

// 删除单个历史记录
const removeHistoryItem = (index: number) => {
    localSearchHistory.value.splice(index, 1)
    saveSearchHistory()
}

// 处理关闭事件
const handleClose = () => {
    emit('close')
}

// 暴露给父组件的方法
defineExpose({
    addSearchHistory,
})
</script>

<style lang="scss" scoped>
$bg-color: #202222;
$border-color: #32393c;
$text-primary: #ffffff;
$text-secondary: #bec7ca;

.search-history {
    position: absolute;
    top: 100%;
    left: 10px;
    right: 10px;
    margin-top: 20px;
    background: #2c3031;
    border-radius: 16px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    padding: 16px;
    z-index: 100;
    border: 1px solid $border-color;
    border-top: none;

    .search-tip {
        color: #8a9294;
        font-size: 24px;
        text-align: center;
        padding: 0;
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 20px 0;
        h3 {
            color: $text-primary;
            font-size: 28px;
            font-weight: 600;
            margin: 0;
        }

        .clear-btn {
            background: transparent;
            border: none;
            color: $text-secondary;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
        }

        .close-btn-wrapper {
            cursor: pointer;
            padding: 4px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;

            .close-btn {
                color: $text-secondary;
                font-size: 20px;
                line-height: 1;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }
    }

    .history-section,
    .recommend-section {
        margin-bottom: 24px;
    }

    .history-items {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
    }

    .history-item {
        display: inline-flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 40px;
        padding: 8px 16px;
        gap: 8px;
        cursor: pointer;

        .history-text {
            color: $text-secondary;
            font-size: 24px;
        }

        .delete-icon {
            color: $text-secondary;
            font-size: 24px;
            cursor: pointer;

            &:hover {
                color: $text-primary;
            }
        }
    }

    .game-items {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
    }

    .game-item {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 40px;
        padding: 12px 20px;
        color: $text-secondary;
        font-size: 24px;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
            background: rgba(255, 255, 255, 0.15);
        }
    }
}
</style>
