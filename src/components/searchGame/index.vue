<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 15:59:30
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-08-05 16:17:05
 * @FilePath     : /src/components/searchGame/index.vue
 * @Description  : 游戏搜索页面内容组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 15:59:30
-->

<template>
    <div class="search-game-container">
        <!-- 1. 导航条 -->
        <SearchHeader />

        <!-- 2. 搜索栏 -->
        <SearchBar v-model="searchKeyword" @search="performSearch" />

        <!-- 3. 分类标签 -->
        <GameCategories :selected-category="selectedCategory" @category-select="selectCategory" />

        <!-- 4. 排序筛选器 -->
        <GameFilters
            v-model:sort-by="sortBy"
            v-model:provider="provider"
            :filtered-games="filteredGames"
            :games-before-provider-filter="gamesBeforeProviderFilter"
        />

        <!-- 5. 游戏列表 -->
        <GameGrid :games="filteredGames" />

        <!-- 6. 回到顶部按钮 -->
        <van-back-top
            :style="{
                'background-color': '#0006',
                right: '16px',
                bottom: '16px',
            }"
            target=".search-game-container"
            ><img src="@/assets/img/home/<USER>" alt="" />
        </van-back-top>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onActivated } from 'vue'
import { useBaseStore } from '@/stores'
import { Categorys } from '@/api/home/<USER>'
import { buildAllGamesList, filterGamesByCategory, filterGamesByKeyword, filterGamesByProvider, sortGames } from './gameSearchUtils'
import SearchHeader from './SearchHeader.vue'
import SearchBar from './SearchBar.vue'
import GameCategories from './GameCategories.vue'
import GameFilters from './GameFilters.vue'
import GameGrid from './GameGrid.vue'

// 常量定义
const MIN_SEARCH_LENGTH = 3 // 最小搜索字符数

const store = useBaseStore()

// 搜索相关状态
const searchKeyword = ref('')

// 分类和筛选状态
const selectedCategory = ref('all')
const sortBy = ref('Default')
const provider = ref('All')

// 获取所有游戏数据（从 store 中获取真实数据）
const allGames = computed(() => {
    return buildAllGamesList(store)
})

// 计算属性：未按厂商筛选的游戏列表
const gamesBeforeProviderFilter = computed(() => {
    // 按分类过滤
    let filtered = filterGamesByCategory(allGames.value, selectedCategory.value)

    // 按搜索关键词过滤（只有当关键词长度>=最小搜索长度时才进行过滤）
    if (searchKeyword.value && searchKeyword.value.length >= MIN_SEARCH_LENGTH) {
        filtered = filterGamesByKeyword(filtered, searchKeyword.value, store)
    }

    return filtered
})

// 计算属性：完全过滤后的游戏列表
const filteredGames = computed(() => {
    let filtered = filterGamesByProvider(gamesBeforeProviderFilter.value, provider.value)

    filtered = sortGames(filtered, sortBy.value)

    return filtered
})

// 方法
const performSearch = (keyword: string) => {
    // 更新搜索关键词，这会触发 computed 属性重新计算，从而过滤游戏列表
    searchKeyword.value = keyword
}

const selectCategory = (category: Categorys) => {
    selectedCategory.value = category.type || 'all'

    // 切换分类时重置供应商选择为 'All'
    // 因为不同分类下的厂商列表会发生变化
    provider.value = 'All'
}

// 组件激活时重置搜索状态（保持缓存但清空搜索内容）
onActivated(() => {
    // 重置搜索关键词
    searchKeyword.value = ''
    selectedCategory.value = 'all'
    sortBy.value = 'Default'
    provider.value = 'All'
})
</script>

<style lang="scss" scoped>
$text-primary: #ffffff;

.search-game-container {
    background: #202222;

    margin-top: constant(safe-area-inset-top);
    margin-top: env(safe-area-inset-top);
    height: calc(var(--vh, 1vh) * 100 - constant(safe-area-inset-top) - constant(safe-area-inset-bottom));
    height: calc(var(--vh, 1vh) * 100 - env(safe-area-inset-top) - env(safe-area-inset-bottom));
    display: flex;
    flex-direction: column;
    color: $text-primary;
    overflow: auto;
}
</style>
