<template>
    <!-- <div> -->
    <div v-if="store.showDown" class="footer-down-wrap">
        <div class="down-close" @click="store.showDownload = false"><img src="@/assets/img/home/<USER>" /></div>
        <div class="down-content">
            <div class="down-tip">
                <div
                    v-html="
                        $t('Download_prompt_text2', {
                            text: $t(`${store.wallet?.currency || store.defaultCurrency.currency}_d_text_num`),
                        })
                    "
                ></div>
                {{
                    $t('Download_prompt_text3', {
                        text: $t(`${store.wallet?.currency || store.defaultCurrency.currency}_d_text_num`),
                    })
                }}
            </div>
            <div class="down-btn" @click="download">{{ $t('Download') }}</div>
        </div>
    </div>
    <div ref="footDom" class="footer" :class="{ isWhite }">
        <div
            :class="['l-button', { active: currentTab === index }]"
            v-for="(item, index) in computedTabMenu"
            :key="item.title"
            @click="tab(item, index)"
        >
            <div class="footer-tab">
                <img class="footer-icon" :src="getImg(item, currentTab === index)" />
                <span class="footer-name" :class="{ active: currentTab === 2 }">{{ $t(item.title) }}</span>
            </div>
            <transition name="grow">
                <div class="grow-line" v-show="currentTab === index"></div>
            </transition>
        </div>
    </div>
    <!-- </div> -->
</template>
<script setup lang="ts" name="BaseFooter">
import { useI18n } from 'vue-i18n'
import { useBaseStore } from '@/stores'
import { useJackpotStore } from '@/stores/jackpotStore'
import { storeToRefs } from 'pinia'
import { getDeviceInfo, getUrlParams, getVipBadge } from '@/utils'
import { Log } from '@/api/log'

const path = '../assets/img/footer-bar/'
const file = import.meta.glob('../assets/img/footer-bar/*', { eager: true })

const i18n = useI18n()
const store = useBaseStore()
const jackpotStore = useJackpotStore()
const { showJackpot } = storeToRefs(jackpotStore)
const showDownload = ref(false)

const props = defineProps({
    initTab: {
        type: Number,
        default: () => 0,
    },
    isWhite: {
        type: Boolean,
        default: () => false,
    },
})
const router = useRouter()
const route = useRoute()
const isRefresh = reactive<{
    [key: string]: boolean
    isRefresh1: boolean
    isRefresh2: boolean
}>({
    isRefresh1: false,
    isRefresh2: false,
})
// 当前显示的tab记录
const currentTab = ref(props.initTab)
const footDom = ref(null)
// tab数据
const jackpotMenuItem = {
    title: 'Jackpot',
    path: '/jackpot',
    icon: 'jackpot',
}

const reelsMenuItem = {
    title: 'Home_Reels',
    path: '/video',
    icon: 'video',
}

const baseTabMenu = [
    {
        title: 'Home_page',
        path: '/',
        icon: 'home',
        isFresh: true,
    },
    // 第二项会根据 showJackpot 动态变化
    jackpotMenuItem,
    {
        title: 'earn_money',
        path: '/earnCash',
        icon: 'earncash',
    },
    {
        title: 'Home_Wallets',
        path: '/wallets',
        icon: 'wallets',
    },
    {
        title: 'Home_events',
        path: '/events',
        icon: 'events',
    },
]

// 计算属性：根据showJackpot值动态生成菜单
const computedTabMenu = computed(() => {
    const menu = [...baseTabMenu]
    // 如果showJackpot为false，则显示Reels菜单项
    // if (!showJackpot.value) {
    //     menu[1] = reelsMenuItem
    // }
    return menu
})

// 跳转tab
const tab = (item, index) => {
    Log({
        event: `tab_${i18n.t(item.title)}`,
    })
    // 如果有刷新功能，并且是点击的当前tab
    if (item.isFresh && currentTab.value === index) {
        refresh(index)
    } else {
        if (item.path === '/earnCash') {
            Log({
                event: 'tab_earn',
            })
        }
        router.push(item.path)
    }
}

const download = () => {
    if (getDeviceInfo().os === 'iOS') {
        showDownload.value = true
    } else {
        const id = getUrlParams('userId') || route.query.userId
        const partner = getUrlParams('partner') || route.query.partner || 66666666
        window.open(`https://betfugu.com/app_ph.html?partner=${partner}${id ? '&userId=' + id : ''}`)
    }
}

// 刷新页面
const refresh = (index) => {
    isRefresh['isRefresh' + index] = !isRefresh['isRefresh' + index]
    setTimeout(() => {
        isRefresh['isRefresh' + index] = !isRefresh['isRefresh' + index]
    }, 2000)
}
const getImg = (item, active = false) => {
    const imgPath = path + `${active ? item.icon + '_active' : item.icon}.png`
    const imgModule = file[imgPath] as any
    return imgModule?.default || ''
}
const getFootReact = () => {
    return footDom.value.getBoundingClientRect()
}
defineExpose({
    getFootReact,
})
</script>
<style scoped lang="scss">
.footer {
    font-size: 25px;
    position: fixed;
    left: 0;
    width: 100%;
    height: var(--footer-height);
    border-top: 2px solid #3d3b38;
    z-index: 100;
    //不用bottom：0是因为，在进行页面切换的时候，vue的transition
    // 会使footer的bottom：0失效，不能准确定位
    // top: calc(var(--vh, 1vh) * 100 - var(--footer-height));
    // background: var(--footer-color);
    bottom: 0px;
    display: flex;
    background-image: linear-gradient(#242833, #242833), linear-gradient(#2a2c3c, #2a2c3c);
    background-blend-mode: normal, normal;
    border-radius: 20px 20px 0px 0px;
    border: solid 2px #303446;

    &.isWhite {
        background: white !important;
        color: #000 !important;
    }

    .l-button {
        flex: 1;
        padding-top: 30px;
        position: relative;
        .footer-tab {
            @apply flex flex-col justify-center items-center;
            position: relative;
        }
        .footer-icon {
            width: 50px;
            height: 50px;
        }
        .footer-name {
            font-size: 20px;
            margin-top: 11px;
            font-weight: 1000;
            color: #668292;
        }
        &:nth-child(4) {
            .footer-name {
                margin-top: 14px;
            }
        }

        &.active {
            .footer-name {
                color: #fff;
            }
        }
    }
}

.footer-down-wrap {
    position: fixed;
    display: flex;
    bottom: var(--footer-height);
    width: 100%;
    height: 136px;
    padding: 0 15px;

    .down-content {
        @apply flex;
        width: 100%;
        height: 94%;
        align-items: center;
        background-image: url('@/assets/img/home/<USER>');
        background-size: cover;
        border-radius: 20px 20px 20px 20px;
        // border: solid 2px #303446;
    }
    .down-icon {
        width: 71px;
        height: 67px;
    }
    .down-tip {
        flex: 1;
        margin: auto;
        font-size: 24px;
        color: #fff;
        line-height: 36px;
        text-align: left;
        font-weight: bold;
        margin-left: 270px;
        :deep(span) {
            color: #2dee88;
            font-size: 26px;
        }
    }
    .down-btn {
        width: 145px;
        height: 55px;
        margin-top: 4px;
        margin-right: 25px;
        line-height: 55px;
        font-size: 34px;
        // background-image: linear-gradient(0deg, #0e8e66 0%, #10c57f 100%), linear-gradient(#887afb, #887afb);
        background-image: linear-gradient(90deg, #fffc7f 0%, #dfff6d 100%), linear-gradient(#8b0500, #8b0500);
        font-size: 24px;
        text-align: center;
        border-radius: 10px;
        font-weight: bold;
        color: #000;
    }
    .down-close {
        position: absolute;
        width: 40px;
        height: 40px;
        left: 18px;
        top: 5px;

        img {
            width: 100%;
            height: 100%;
        }
    }
}
</style>
