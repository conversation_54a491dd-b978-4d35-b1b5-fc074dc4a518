import request from '@/utils/request'
import { ResultData } from '../types'
import { Homelist, InitLangAndCurrency, RegistGiftData, Package } from './type'
import { GLOBAL_URL } from '@/enums'
import { size } from 'pinus'
// ResultData
// 获取首页数据
export const getHomeList = (loading = true): Promise<ResultData<Homelist>> => {
    return request({
        url: '/opendata/homepage/indexV3',
        method: 'post',
        data: {},
        loading,
    })
}
// 获取history数据
export const getHomeHis = (loading = true): Promise<ResultData<Homelist>> => {
    return request({
        url: GLOBAL_URL.GetHis,
        method: 'get',
        data: {},
        loading,
    })
}

// 初始化语言和货币
export const initLangAndCurrency = (params: { language: string; currency: string }): Promise<ResultData<InitLangAndCurrency>> => {
    return request({
        url: GLOBAL_URL.InitLangAndCurrency,
        method: 'post',
        data: params,
    })
}

export const getConfig = (name: string): Promise<ResultData<{}>> => {
    return request({
        url: GLOBAL_URL.GetConfig,
        method: 'get',
        params: {
            name,
        },
    })
}
// GetEvents
export const getEvents = (): Promise<ResultData<Banner[]>> => {
    return request({
        url: GLOBAL_URL.GetEvents,
        method: 'get',
        params: {},
    })
}

// resellerAutoReplay
export const getResellerAuto = (): Promise<{
    code: number
    reply: string
}> => {
    return request({
        url: GLOBAL_URL.ResellerAutoReplay,
        method: 'get',
        params: {},
        showTip: false,
    })
}
export const setResellerAuto = (reply: string): Promise<{ code: number }> => {
    return request({
        url: GLOBAL_URL.ResellerAutoReplay,
        method: 'post',
        data: { reply },
    })
}
// 注册礼物
export const getRegistGifts = (): Promise<ResultData<RegistGiftData>> => {
    return request({
        url: GLOBAL_URL.RegistGifts,
        method: 'get',
        data: {},
        loading: false,
    })
}
export const claimRegistGifts = (): Promise<ResultData<RegistGiftData>> => {
    return request({
        url: GLOBAL_URL.ClaimRegistGifts,
        method: 'post',
        data: {},
        loading: false,
    })
}

//
export const getfreepackage = (loading1): Promise<{ code: number } & Package> => {
    return request({
        url: GLOBAL_URL.Getfreepackage,
        method: 'post',
        data: {},
        loading: false,
    })
}

export const getLastestBet = (): Promise<{ code: number } & Package> => {
    return request({
        url: GLOBAL_URL.LastestBet,
        method: 'get',
        params: {
            from: 0,
            size: 50,
        },
        loading: false,
        showTip: false,
    })
}
export const getHighRoller = (): Promise<{ code: number } & Package> => {
    return request({
        url: GLOBAL_URL.HighRoller,
        method: 'get',
        params: {
            from: 0,
            size: 10,
        },
        loading: false,
        showTip: false,
    })
}
export const getHighMultiplier = (): Promise<{ code: number } & Package> => {
    return request({
        url: GLOBAL_URL.HighMultiplier,
        method: 'get',
        params: {
            from: 0,
            size: 10,
        },
        loading: false,
        showTip: false,
    })
}

// 获取公告
export const getH5Announcement = (params = {}): Promise<{ code: number } & Package> => {
    return request({ url: GLOBAL_URL.H5Announcement, method: 'post', params, loading: false, showTip: false })
}
