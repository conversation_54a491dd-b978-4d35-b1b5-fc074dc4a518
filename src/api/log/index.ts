import request from '@/utils/request'
import { Result } from '../types'
import { GLOBAL_URL } from '@/enums'
import { useBaseStore } from '@/stores'
import { getDeviceInfo } from '@/utils'

interface Params {
    logname?: string
    event?: string
    content?: {
        id?: string
        uid?: number
        progress?: number
        isEnd?: boolean
        [key: string]: any
    }
}
export function Log(params: Params): Promise<Result> {
    return
    const store = useBaseStore()
    const { content } = params
    return request({
        url: GLOBAL_URL.Log,
        method: 'post',
        data: {
            logname: 'event-click',
            ...params,
            content: {
                ...(content || {}),
                uid: store.userInfo?.uid,
                token: store.token,
                deviceInfo: getDeviceInfo(),
                app: store.getApp,
            },
        },
        loading: false,
        showTip: false,
    })
}
