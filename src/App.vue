<template>
    <!-- 全局SVG图标定义 -->
    <SvgIcons />

    <van-overlay class="flex items-center justify-center" :show="store.loading" :z-index="6000">
        <van-loading type="spinner" />
    </van-overlay>
    <van-overlay class="flex flex-col items-center justify-center" :show="store.conneting" :z-index="6001">
        <p class="login-tip">Logging in...</p>
        <van-loading type="spinner" />
    </van-overlay>

    <!-- <langugeSet /> -->
    <router-view v-slot="{ Component }">
        <transition :name="transitionName">
            <keep-alive :exclude="store.excludeNames" :max="3">
                <component :is="Component" />
            </keep-alive>
        </transition>
    </router-view>
    <!-- <EarnAnimate v-if="NO_ANIMATION.includes(route.path as string)" /> -->
</template>

<script setup lang="tsx" name="app">
// Extend the Window interface to include receiveMessageFromAndroid
declare global {
    interface Window {
        receiveMessageFromAndroid?: (event: string, data: any) => void
    }
}

import { useBaseStore } from '@/stores'
import type { RouteRecordRaw } from 'vue-router'
import routes from '@/router/route'
import { NO_ANIMATION } from '@/enums'
import { Session } from '@/pomelo2/session'
import { useMonitorMsg } from '@/hooks/useMonitorMsg'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import bus, { EVENT_KEY } from '@/utils/bus'
import { getStatusBarHeight, getLinkId, getUrlParams } from '@/utils/index'
import SvgIcons from '@/components/common/SvgIcons.vue'
// import langugeSet from '@/components/dialog/langugeSet.vue'
import qs from 'qs'
import { Log } from '@/api/log'
import dayjs from 'dayjs'
import { setLocaleLang } from '@/language'
// import { getDefault } from '@/api/servo'
// import EarnAnimate from '@/components/animate.vue'

import FingerprintJS from '@fingerprintjs/fingerprintjs'
// Initialize an agent at application startup.
const fpPromise = FingerprintJS.load()
;(async () => {
    // Get the visitor identifier when you need it.
    const fp = await fpPromise
    const result = await fp.get()
    console.log('visitorId', result.visitorId)
    localStorage.setItem('visitorId', result.visitorId)
})()

const EarnAnimate = defineAsyncComponent({
    // 加载函数
    loader: () => import('@/components/animate.vue'),
    delay: 5000,
})

const store = useBaseStore()
const route = useRoute()
const i18n = useI18n()
const router = useRouter()

const { monitor, unBindMonitor } = useMonitorMsg()

const transitionName = ref('go')

const navType = ref('back')

// watch $route 决定使用哪种过渡
const findPath = (v, to) => {
    if (v.children) {
        const find = v.children.find((item) => item.path === to)
        return find ? true : false
    } else {
        return v.path === to
    }
}
const connectGameServer = (link: string) => {
    store.conneting = true
    store.gameserver = new Session(link, {
        cert: undefined,
        // @ts-ignore
        auth: async function () {
            if (store.token) {
                store.conneting = false
                store.SessionLogin()
                monitor()
                bind()
            }
        },
        localstorage: localStorage,
        timeout: 1000,
        retry: 1,
    })
    store.gameserver.on('gone', () => {
        unBindMonitor()
        const serverUrl = store.getWsServer
        if (serverUrl) {
            connectGameServer(serverUrl)
        } else {
            store.conneting = false
            Log({
                event: 'ws_gone',
                content: {
                    ctime: dayjs().format('YYYY-MM-DD HH:mm:ss.SSS'),
                    retryCounter: store.gameserver.retryCounter,
                    host: store.gameserver._remote.href,
                },
            })
            showDialog({
                message: 'Unable to connect to game service, please try again later',
                className: 'common-dialog',
                showCancelButton: false,
                confirmButtonText: i18n.t('Confirm'),
            }).then(() => {
                console.log(999, route.path)
                if (route.path === '/iframe') {
                    router.back()
                }
                store.wsIdx = 0
                connectGameServer(store.getWsServer)
            })
        }
    })
    store.gameserver.on('kickout', (reason) => {
        if (reason.reason === '{"code":-1,"msg":"login from other device!"}') {
            console.log(reason, 'kickout')
            Log({
                event: 'ws_kickout',
                content: {
                    ctime: dayjs().format('YYYY-MM-DD HH:mm:ss.SSS'),
                    retryCounter: store.gameserver.retryCounter,
                    host: store.gameserver._remote.href,
                },
            })
            store.logOut()
            bus.emit(EVENT_KEY.KICK_OUT)
            showDialog({
                message: i18n.t('Abnormal_account_check') + '\n' + i18n.t('Any_question_CS'),
                className: 'common-dialog',
                showCancelButton: false,
                confirmButtonText: i18n.t('Confirm'),
            }).then(() => {
                router.replace('/login')
            })
        }
    })
}
watch(
    () => route.path,
    (to, from) => {
        store.setMaskDialog({ state: false, mode: store.maskDialogMode })
        if ((NO_ANIMATION.indexOf(from) !== -1 && NO_ANIMATION.indexOf(to) !== -1) || !from) {
            return (transitionName.value = 'nomal')
        }

        // 路由配置前后顺序代表动画
        const toDepth = routes.findIndex((v: RouteRecordRaw) => findPath(v, to))
        const fromDepth = routes.findIndex((v: RouteRecordRaw) => findPath(v, from))

        transitionName.value = toDepth >= fromDepth ? (navType.value === 'go' ? 'go' : 'nomal') : navType.value === 'back' ? 'back' : 'nomal'

        // 清理标记（避免影响后续导航）
        navType.value = ''
    },
    {
        immediate: true,
    }
)
watch(
    () => store.token,
    (oldVal, newVal) => {
        if (store.token) {
            if (oldVal && newVal) {
                return
            }
            connectGameServer(store.getWsServer)
        }
    },
    {
        immediate: true,
    }
)
watch(
    () => store.wallet?.currency,
    () => {
        store.clearLamp()
    }
)
function setDefaultLang() {
    let lang = store.lang
    if (!lang) {
        const tgLang = store.isTg ? store.languageList.find((item) => store.tgInitData.language_code === item.value) : ''
        const uaLang = store.languageList.find((item) => navigator.language === item.value)
        if (tgLang) {
            lang = tgLang.value
        } else if (uaLang) {
            lang = uaLang.value
        } else {
            lang = store.languageList[0].value
        }
    }
    console.log(lang, 'lang')
    setLocaleLang(lang)
}

const bind = async () => {
    Log({
        event: 'share-bind',
        content: {
            type: 'start',
            shareid: store.shareId,
        },
    })
    if (store.shareId) {
        const res = await store.bindInviter(
            {
                shareid: store.shareId, // 邀请人id
                auto: true, //是否自动绑定
                from: store.sharefrom, //渠道来源
            },
            { loading: false, tip: false }
        )
        Log({
            event: 'share-bind',
            content: {
                type: 'result',
                shareid: store.shareId,
                value: res,
            },
        })
        if (res?.code === 200) {
            store.$patch({
                shareId: 0,
            })
        }
    }
}

// 计算1vh实际高度， 解决浏览器搜索栏问题
function resetVhAndPx() {
    const vh = window.innerHeight * 0.01
    document.documentElement.style.setProperty('--vh', `${vh}px`)
    document.documentElement.style.setProperty('--safe-height', `${getStatusBarHeight()}px`)
}

const linkId = getLinkId()
if (linkId) {
    localStorage.setItem('pwa-link-id', linkId as string)
    localStorage.setItem('is-self', 'true')
}
function isLandscape() {
    // 竖屏
    // if (window.matchMedia('(orientation: landscape)').matches) {
    const isPortrait = window.innerHeight > window.innerWidth
    if (isPortrait) {
        // setTimeout(() => {
        //     resetVhAndPx()
        //     console.log(window.innerHeight, 'window.innerHeight')
        // }, 100)
        if (window.requestAnimationFrame) {
            requestAnimationFrame(() => {
                requestAnimationFrame(() => {
                    resetVhAndPx()
                    console.log(window.innerHeight, 'window.innerHeight')
                })
            })
        } else {
            setTimeout(() => {
                resetVhAndPx()
                console.log(window.innerHeight, 'window.innerHeight')
            }, 100)
        }
    }
}
function handleToLogin() {
    router.push({
        path: '/login',
        query: {
            path: route.path,
            params: qs.stringify(route.query),
        },
    })
}
onBeforeMount(() => {
    const params = new URLSearchParams(window.location.search)
    let urlParams = {}
    Array.from(params.entries()).forEach(([key, value]) => {
        urlParams[key] = value
    })
    console.log('urlParams', urlParams)
    const zgparams = JSON.parse(localStorage.getItem('__zg_4779402644807_params')) || {}
    if (zgparams) {
        Object.keys(zgparams).forEach((key) => {
            urlParams[key] = zgparams[key]
        })
    }
    let link_id = JSON.parse(localStorage.getItem('__zg_4779402644807_link_id')) || urlParams['link_id']
    if (link_id) {
        store.$patch({
            link_id: link_id,
        })
    }
    const promote_url_id = JSON.parse(localStorage.getItem('__zg_4779402644807_promote_url_id')) || urlParams['promote_url_id']
    if (promote_url_id) {
        store.$patch({
            promote_url_id: promote_url_id,
        })
    }
    console.log('promote_url_id', promote_url_id, 'link_id', link_id, urlParams)

    const id = getUrlParams('userId') || route.query.userId || localStorage.getItem('userId') || urlParams['userId']
    Log({
        event: 'share-bind',
        content: {
            type: 'before',
            shareid: id,
        },
    })
    if (id) {
        store.$patch({
            shareId: +id,
        })
    }

    const from = getUrlParams('sfrom') || route.query.sfrom || localStorage.getItem('sfrom') || urlParams['sfrom']
    if (from) {
        store.$patch({
            sharefrom: from,
        })
    }

    const partner = getUrlParams('partner') || route.query.partner || localStorage.getItem('partner') || urlParams['partner']
    if (partner) {
        store.$patch({
            partner: +partner,
        })
    }

    const ntvfrom = getUrlParams('ntvfrom') || route.query.ntvfrom || localStorage.getItem('ntvfrom') || urlParams['ntvfrom']
    if (ntvfrom) {
        store.$patch({
            ntvfrom: ntvfrom,
        })
    }
})
onMounted(() => {
    Log({
        event: 'h5 app init',
    })

    const originalBack = router.back
    router.back = () => {
        navType.value = 'back'
        originalBack()
    }

    const originalGo = router.go
    router.go = (delta) => {
        navType.value = 'go'
        originalGo(delta)
    }

    resetVhAndPx()
    // 监听resize事件 视图大小发生变化就重新计算1vh的值
    window.addEventListener('resize', () => {
        resetVhAndPx()
        bus.emit('resize')
    })
    window.addEventListener('orientationchange', isLandscape)

    window.receiveMessageFromAndroid = (event: string, data: any) => {
        console.log('data', data, event)
        bus.emit(event, data)
    }
    document.addEventListener('visibilitychange', function () {
        if (document.visibilityState === 'visible') {
            // 页面变为可见状态时执行的操作
            setTimeout(() => {
                resetVhAndPx()
            }, 500)
        }
    })

    document.documentElement.style.setProperty('--safe-height', `${getStatusBarHeight()}px`)
    // window.addEventListener('beforeunload', function (e) {
    //     console.log('beforeunload')
    //     store.gameLogOut()
    //     const confirmationMessage = ''
    //     e.returnValue = confirmationMessage
    //     return confirmationMessage
    // })
})

onBeforeMount(() => {
    setDefaultLang()
    bus.on('toLogin', handleToLogin)
})
onBeforeUnmount(() => {
    bus.off('toLogin', handleToLogin)
})
</script>
<style lang="scss">
#app {
    height: calc(var(--vh, 1vh) * 100);
    // width: 100%;
    min-height: 100vh;
    position: relative;
    padding-top: max(env(safe-area-inset-top), var(--safe-height));
    overflow: hidden;
}

@media screen and (min-width: 500px) {
    #app {
        // width: 500px !important;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
    }
}
.go-enter-from {
    transform: translate3d(100%, 0, 0);
}

//最终状态
.back-enter-to,
.back-enter-from,
.go-enter-to,
.go-leave-from {
    transform: translate3d(0, 0, 0);
}

.go-leave-to {
    transform: translate3d(-100%, 0, 0);
}

.go-enter-active,
.go-leave-active,
.back-enter-active,
.back-leave-active {
    transition: all 0.2s ease-in;
}
.nomal-enter-to,
.nomal-enter-active,
.back-enter-to,
.go-enter-to,
.go-enter-active,
.back-enter-active {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
}
.back-enter-from {
    transform: translate3d(-100%, 0, 0);
}

.back-leave-to {
    transform: translate3d(100%, 0, 0);
}
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}
.login-tip {
    margin-bottom: 10px;
    font-size: 27px;
    color: #fff;
}
</style>
