import axios, { AxiosInstance, AxiosError, AxiosResponse } from 'axios'
import { showFailToast } from 'vant'
import qs from 'qs'
import { getCommonHeaders } from '@/utils'
import { useBaseStore } from '@/stores'
import { ERROR_CODES } from '@/enums'
import { getErrorMsg } from '@/utils'
import i18n from '@/language'

// interface CustomAxiosRequestConfig extends InternalAxiosRequestConfig {
//     loading?: boolean // 可选的 loading 属性
// }

const commonConfig = {
    baseURL: import.meta.env.VITE_API_URL,
    timeout: 50000,
    headers: { 'Content-Type': 'application/json;charset=UTF-8' },
    paramsSerializer: {
        serialize(params) {
            return qs.stringify(params, { allowDots: true })
        },
    },
}

// 配置新建一个 axios 实例
const service: AxiosInstance = axios.create({ ...commonConfig, baseURL: import.meta.env.VITE_API }) // 常规服务
const serVo = axios.create({ ...commonConfig, baseURL: `${import.meta.env.VITE_API_URL}/servo` }) // 获取服务器配置服务
const uploadServer = axios.create({ ...commonConfig, baseURL: import.meta.env.VITE_API_UPLOAD }) // 上船服务
const paymentServer = axios.create({ ...commonConfig, baseURL: import.meta.env.VITE_API_PAYMENT }) // 支付服务
// import.meta.env.VITE_API, import.meta.env.VITE_API_URL, import.meta.env.VITE_API_UPLOAD, import.meta.env.VITE_API_PAYMENT
const serviceList = [service, serVo, uploadServer, paymentServer]

serviceList.forEach((service) => {
    // 添加请求拦截器
    service.interceptors.request.use(
        (config) => {
            console.log('\x1b[36m请求路由:\x1b[0m' + config?.url + '  \x1b[32m请求参数:\x1b[0m', config?.data)
            if (config.loading) {
                console.trace('trace-----------1', config?.url)
            }
            const store = useBaseStore()
            store.loading = typeof config.loading === 'boolean' ? config.loading : true
            // 在发送请求之前做些什么 token
            if (store.token) {
                config.headers!['access-token'] = `${store.token}`
            }
            Object.assign(config.headers, getCommonHeaders())
            return config
        },
        (error) => {
            // 对请求错误做些什么
            return Promise.reject(error)
        }
    )
    // & { config: CustomAxiosRequestConfig }
    // 添加响应拦截器
    service.interceptors.response.use(
        (response: AxiosResponse) => {
            console.log('\x1b[34m响应路由:\x1b[0m' + response?.config?.url + '  \x1b[35m响应数据:\x1b[0m', response?.data)

            const store = useBaseStore()
            store.loading = false
            // 对响应数据做点什么
            const res = response.data
            if (res.code && res.code !== ERROR_CODES.OK) {
                // `token` 过期或者账号已在别处登录
                const showTip = typeof response.config.showTip === 'boolean' ? response.config.showTip : true
                if (showTip) {
                    const tip = getErrorMsg(response.config.url, res.code)
                    showToast({
                        message: i18n.global.t(tip),
                        zIndex: 4000,
                    })
                }

                // @ts-ignore
                return res
            } else {
                return res
            }
        },
        (error: AxiosError) => {
            const store = useBaseStore()
            store.loading = false
            if (error.config.url === 'https://pwa-backend-prod.roibest.com/fbclid/get') {
                return
            }
            // 对响应错误做点什么
            if (error.message.indexOf('timeout') != -1) {
                showFailToast(i18n.global.t('Network Timeout'))
            } else if (error.message == 'Network Error') {
                console.trace()

                showFailToast(i18n.global.t('Network_error_tryagain'))
            } else {
                if (error.response?.data) showFailToast(error.response.statusText)
                else showFailToast(i18n.global.t('Interface path not found'))
            }
            return Promise.reject(error)
        }
    )
})
export { serVo, uploadServer, paymentServer }
// 导出 axios 实例
export default service
