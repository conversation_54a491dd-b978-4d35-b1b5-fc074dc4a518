import i18n from '@/language'
import { useBaseStore } from '@/stores'
import { ERROR_MSG } from '@/enums'
import { decode } from 'js-base64'
import { useRoute } from 'vue-router'
import webApp from '@twa-dev/sdk'
import eventBus from '@/utils/bus'

// 获取静态文件地址
export const getAssetUrl = (url) => {
    return new URL('/images' + url, import.meta.url).href
}

export function _stopPropagation(e: Event) {
    e.stopImmediatePropagation()
    e.stopPropagation()
    e.preventDefault()
}
// 获取设备信息
export const getDeviceInfo = () => {
    const ua = navigator.userAgent
    const platform = navigator.platform

    const deviceInfo = {
        userAgent: ua,
        platform: platform,
        os: '',
    }

    // 检测操作系统
    if (/iPhone|iPad|iPod/i.test(ua)) {
        deviceInfo.os = 'iOS'
    } else if (/Android/i.test(ua)) {
        deviceInfo.os = 'Android'
    } else if (/Windows Phone/i.test(ua)) {
        deviceInfo.os = 'Windows Phone'
    } else if (webApp.initData) {
        deviceInfo.os = webApp.platform
    } else {
        deviceInfo.os = 'Unknown'
    }

    return deviceInfo
}
// export const plantform = {}

const { VITE_APPID, VITE_PUBLISHER, VITE_CHANNEL, VITE_TG_CHANNEL } = import.meta.env

// 获取请求公共header
export const getCommonHeaders = () => {
    const store = useBaseStore()
    const currency = store.wallet?.currency
    return {
        appid: VITE_APPID,
        publisher: VITE_PUBLISHER,
        channel: webApp.initData ? VITE_TG_CHANNEL : VITE_CHANNEL,
        version: __APP_VERSION__,
        pkgname: 'h5',
        basepkgname: 'h5',
        deviceid: '',
        // @ts-ignore
        network: (navigator.connection || navigator.mozConnection || navigator.webkitConnection)?.effectiveType || '',
        osv: '10',
        platform: getDeviceInfo().os,
        // @ts-ignore
        language: currency ? i18n.global.locale.value : store.defaultLangguage.language,
        // @ts-ignore
        currency: currency ? currency : '',
        partner: store.partner,
    }
}
// 获取ws游戏服务登录的请求头
export const getWsHeader = () => {
    const store = useBaseStore()
    const { area, city, country, eu, ip, range, region, timezone, ll, metro } = store.servoConfig.geo || {}
    const { telcontry } = store.userInfo || {}
    const deviceid = localStorage.getItem('visitorId') || '(null)'
    return {
        ...getCommonHeaders(),
        // @ts-ignore
        nettype: navigator.connection?.effectiveType || '',
        telcontry, // 手机号国家
        appVer: '',
        trackerToken: '',
        trackerName: '',
        range,
        country,
        region,
        eu,
        timezone,
        city,
        area,
        ip,
        ll,
        metro,
        timestamp: Date.now(),
        grand_type: '',
        lasttimestamp: Date.now(),
        deviceid,
    }
}
// 获取返回的错误码提示语
export const getErrorMsg = (url, code) => {
    let txt = (ERROR_MSG[url] && ERROR_MSG[url][code]) || 'Network_error_tryagain'
    console.trace('trace-----------3', txt)
    return (ERROR_MSG[url] && ERROR_MSG[url][code]) || 'Network_error_tryagain'
}
// 检测是否能非静音自动播放
export function chekVideoMuteState() {
    return new Promise((resolve) => {
        // 创建一个新的AudioContext
        // @ts-ignore
        const audioContext = new (window.AudioContext || window.webkitAudioContext)()

        // 创建一个输出音频节点
        const output = audioContext.createGain()
        output.gain.value = 0 // 将音量设置为0
        output.connect(audioContext.destination)

        // 创建一个短暂的音频节点
        const oscillator = audioContext.createOscillator()
        oscillator.connect(output)

        // 开始播放音频
        oscillator.start()
        oscillator.stop(audioContext.currentTime + 0.1) // 100ms后停止播放

        // 在停止播放后，检查输出音频节点的音量
        setTimeout(() => {
            resolve(output.gain.value > 0.001) // 如果音量大于0.001，则可以非静音播放
        }, 100) // 给音频播放和检测足够的时间
    })
}
export function random(min, max) {
    const minCeiled = Math.ceil(min)
    const maxFloored = Math.floor(max)
    return Math.floor(Math.random() * (maxFloored - minCeiled + 1) + minCeiled) // 包含最小值和最大值
}

export function sort(list, key = 'rank') {
    return list?.sort((a, b) => a[key] - b[key]) || []
}
interface Lang {
    lang_map: {
        [key: string]: {
            label: string
            value: string
            key: string
            icon: string
        }[]
    }
    indexList: string[]
}
// 获取语言配置
export function getLangugeConfig(options): Lang {
    const indexList = [...new Set(options.map((item) => item.key))] as string[]
    const lang_map = {}
    options.forEach((item) => {
        const keyList = lang_map[item.key] || []
        keyList.push(item)
        lang_map[item.key] = keyList
    })
    return {
        lang_map,
        indexList,
    }
}

export function getLocalLang() {
    const local = localStorage.getItem('BetFugu_base')
    const store = local ? JSON.parse(local) : {}
    const lang = navigator.language
    return store.lang || (lang && lang.includes('-') ? lang : 'en-US')
}

// 判断是小米和qq浏览器
export const isMOrQQBrowser = () => {
    return /Mi|MQQBrowser|QQBrowser/i.test(navigator.userAgent)
}
// base64解密昵称
export const decodeName = (nickname) => {
    return nickname && decode(nickname)
}
export const getChannel = (uid, iUid) => {
    return [uid, iUid].sort().join(':')
}
export function encryptUsername(username) {
    if (!username) {
        return ''
    }
    // 如果昵称长度小于等于3，不加密
    if (username.length <= 1) {
        return username
    }

    // 计算显示的首尾字符数量（至少1个，最多2个）
    const displayChars = Math.max(1, (username.length - 3) / 2)

    // 获取昵称的首字符和尾字符
    const firstChars = username.substring(0, displayChars)
    const lastChars = username.substring(username.length - displayChars)

    // 创建一个由 '*' 组成的字符串，最多3个星号
    const encryptedPart = '*'.repeat(Math.min(3, username.length - 2 * displayChars))

    // 拼接首字符、加密部分和尾字符
    return firstChars + encryptedPart + lastChars
}

export function getStatusBarHeight() {
    let statusBarHeight = 0
    // (window.screen && window.matchMedia('(display-mode: standalone)').matches) || window.navigator?.standalone  条件不起作用
    if (localStorage.getItem('__rb_6764554973_link_id') || localStorage.getItem('__betfugu_858956856_data')) {
        // const devicePixelRatio = window.devicePixelRatio
        // const screenHeight = window.screen.height
        statusBarHeight = 20 // 这里的24需要根据设备实际情况进行调整
    }

    return statusBarHeight
}

// 获取linkid
export function getLinkId() {
    const search = location.search
    const query = new URLSearchParams(search)
    const route = useRoute()
    return query.get('linkId') || route.query.linkId || localStorage.getItem('linkId')
}
// 获取telegrame用户信息
export function getUrlQuery(data, key = 'user') {
    //1. 提取user参数的值
    var params = new URLSearchParams(data)
    var encodedJson = params.get(key)
    // 2. 解码URL编码的字符串
    var jsonString = decodeURIComponent(encodedJson)
    // 3. 将字符串转换为JSON对象
    return JSON.parse(jsonString)
}

// 纯粹的登录状态检查（不触发跳转）
export function isLoggedIn() {
    const store = useBaseStore()
    return !!store.token
}
export function checkLogin() {
    const store = useBaseStore()
    return new Promise((resolve, reject) => {
        if (!store.token) {
            eventBus.emit('toLogin')
            reject('no token')
            return false
        }
        resolve(true)
    })
}
export function getRandomPeople(num) {
    // 计算50到99之间的随机数
    const randomFactor = Math.floor(Math.random() * 10) + 10
    // 计算0到99之间的随机数
    const randomOffset = Math.floor(Math.random() * 20)
    // 根据规则计算假数据
    const fakeNum = (num + 1) * randomFactor + randomOffset
    return fakeNum
}
// 获取链接参数
export function getUrlParams(key = '') {
    if (!key) {
        return
    }
    const search = location.search
    const query = new URLSearchParams(search)
    return query.get(key)
}
export function getKeyValue(obj, keys) {
    if (!obj) {
        return
    }
    const ks = keys
        .replace(/\[(\w+)\]/g, '.$1') // 替换 [0] 为 .0
        .split('.')
    // console.log('getKeyValue', obj, ks)

    if (ks.length && ks.length === 1) {
        return obj[ks[0]]
    }
    return ks.reduce((prev, next) => {
        return prev ? prev[next] : ''
    }, obj)
}
export function preloadImages(imagePaths) {
    const promises = imagePaths.map((src) => {
        return new Promise((resolve, reject) => {
            const img = new Image()
            img.src = src
            img.onload = () => resolve(src)
            img.onerror = () => reject(src)
        })
    })

    return Promise.all(promises)
}
// 加载文件夹目录下的所有图片
export function getAllImagesUrl(name) {
    switch (name) {
        case 'welcome':
            return import.meta.glob('@/assets/img/dialog/welcome_bg.png', { eager: true, as: 'url' })
        case 'BindDialog':
            return import.meta.glob('@/assets/img/bind-phone/*', { eager: true, as: 'url' })
        case 'chargeGift':
            return import.meta.glob('@/assets/img/dialog/charge_gift_bg.png', { eager: true, as: 'url' })
        case 'loseReturn':
            return import.meta.glob('@/assets/img/lose_return/*', { eager: true, as: 'url' })
        case 'benefit':
            return import.meta.glob('@/assets/img/benet_dialog/*', { eager: true, as: 'url' })
        default:
            return {}
    }
}
export function changeImgHost(link) {
    //if (['dyfwv57q39dhr.cloudfront.net', 'playtok.win', 'h5test.playtok.win'].includes(location.host)) {
    //    return link.replace('cach.kaxyx.top', location.host)
    //}
    return link
}
export function getHashParams(key) {
    const hashParams = new URLSearchParams(window.location.hash.split('?')[1])
    const params = Object.fromEntries(hashParams)
    return params[key]
}

export function getVipBadge(level: number) {
    var index = 0
    if (level >= 1 && level <= 3) {
        index = 1
    } else if (level >= 4 && level <= 6) {
        index = 2
    } else if (level >= 7 && level <= 9) {
        index = 3
    } else if (level >= 10 && level <= 11) {
        index = 4
    } else if (level >= 12) {
        index = 5
    }
    return index
}
