/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AccountBind: typeof import('./src/components/wallet/accountBind.vue')['default']
    Animate: typeof import('./src/components/animate.vue')['default']
    Announcement: typeof import('./src/components/dialog/announcement.vue')['default']
    Auto: typeof import('./src/components/newHome/auto.vue')['default']
    BackBtn: typeof import('./src/components/backBtn.vue')['default']
    Banner: typeof import('./src/components/newHome/banner.vue')['default']
    Bar: typeof import('./src/components/vip/bar.vue')['default']
    BaseFooter: typeof import('./src/components/BaseFooter.vue')['default']
    Benefit: typeof import('./src/components/dialog/benefit.vue')['default']
    BindDialog: typeof import('./src/components/dialog/BindDialog.vue')['default']
    BindInviter: typeof import('./src/components/dialog/BindInviter.vue')['default']
    BonusDialog: typeof import('./src/components/dialog/bonusDialog.vue')['default']
    BonusPop: typeof import('./src/components/newHome/bonusPop.vue')['default']
    Btip: typeof import('./src/components/wallet/btip.vue')['default']
    Button: typeof import('./src/components/Button.vue')['default']
    Card: typeof import('./src/components/vip/card.vue')['default']
    CashDialog: typeof import('./src/components/dialog/CashDialog.vue')['default']
    ChargeGift: typeof import('./src/components/dialog/chargeGift.vue')['default']
    Chat: typeof import('./src/components/chat/chat.vue')['default']
    ChatItem: typeof import('./src/components/notice/chatItem.vue')['default']
    CheckDialog: typeof import('./src/components/dialog/checkDialog.vue')['default']
    CommonDialog: typeof import('./src/components/dialog/CommonDialog.vue')['default']
    ComPageHeader: typeof import('./src/components/common/ComPageHeader.vue')['default']
    ComShareDialog: typeof import('./src/components/dialog/ComShareDialog.vue')['default']
    Congratulations: typeof import('./src/components/dialog/congratulations.vue')['default']
    Contribution: typeof import('./src/components/earnMoney/contribution.vue')['default']
    CountyMobile: typeof import('./src/components/countyMobile.vue')['default']
    CurrencyDialog: typeof import('./src/components/wallet/currencyDialog.vue')['default']
    DepositRecord: typeof import('./src/components/wallet/depositRecord.vue')['default']
    Dialog: typeof import('./src/components/dialog/Dialog.vue')['default']
    DirectorDialog: typeof import('./src/components/dialog/directorDialog.vue')['default']
    EarnClub: typeof import('./src/components/dialog/earnClub.vue')['default']
    EarnDetail: typeof import('./src/components/earnMoney/earnDetail.vue')['default']
    Events: typeof import('./src/components/notice/events/index.vue')['default']
    FirstRechargeIcon: typeof import('./src/components/newHome/firstRechargeIcon.vue')['default']
    ForUList: typeof import('./src/components/newHome/forUList.vue')['default']
    GameCategories: typeof import('./src/components/searchGame/GameCategories.vue')['default']
    GameFilters: typeof import('./src/components/searchGame/GameFilters.vue')['default']
    GameGrid: typeof import('./src/components/searchGame/GameGrid.vue')['default']
    GameRcord: typeof import('./src/components/wallet/gameRcord.vue')['default']
    GetBounus: typeof import('./src/components/dialog/getBounus.vue')['default']
    GiftCenter: typeof import('./src/components/newHome/giftCenter.vue')['default']
    Header: typeof import('./src/components/Header.vue')['default']
    History: typeof import('./src/components/jackpot/History.vue')['default']
    Home: typeof import('./src/components/newHome/home.vue')['default']
    HomeFloat: typeof import('./src/components/newHome/homeFloat.vue')['default']
    HomeHeader: typeof import('./src/components/newHome/homeHeader.vue')['default']
    HomeJackpotPrize: typeof import('./src/components/jackpot/HomeJackpotPrize.vue')['default']
    HomeTab: typeof import('./src/components/newHome/homeTab.vue')['default']
    HotList: typeof import('./src/components/newHome/hotList.vue')['default']
    Input: typeof import('./src/components/searchGame/input.vue')['default']
    InviteAwardPop: typeof import('./src/components/invitewheel/inviteAwardPop.vue')['default']
    InviteAwardRecord: typeof import('./src/components/invitewheel/inviteAwardRecord.vue')['default']
    InviteboxPopup: typeof import('./src/components/invitewheel/inviteboxPopup.vue')['default']
    InvitecashoutPop: typeof import('./src/components/invitewheel/invitecashoutPop.vue')['default']
    Invitepop: typeof import('./src/components/invitewheel/invitepop.vue')['default']
    Invitespin: typeof import('./src/components/invitewheel/invitespin.vue')['default']
    InviteWheelIcon: typeof import('./src/components/newHome/InviteWheelIcon.vue')['default']
    InvitewheelPopup: typeof import('./src/components/invitewheel/invitewheelPopup.vue')['default']
    Item: typeof import('./src/components/foru/item.vue')['default']
    Jackpot: typeof import('./src/components/jackpot/index.vue')['default']
    JackpotContent: typeof import('./src/components/jackpot/JackpotContent.vue')['default']
    JackpotHeader: typeof import('./src/components/jackpot/JackpotHeader.vue')['default']
    JackpotPrize: typeof import('./src/components/jackpot/JackpotPrize.vue')['default']
    Lamp: typeof import('./src/components/newHome/lamp.vue')['default']
    LangChoose: typeof import('./src/components/language/langChoose.vue')['default']
    Language: typeof import('./src/components/newHome/language.vue')['default']
    LangugeSelect: typeof import('./src/components/newHome/langugeSelect.vue')['default']
    LangugeSet: typeof import('./src/components/dialog/langugeSet.vue')['default']
    Latest: typeof import('./src/components/newHome/latest.vue')['default']
    LevelDown: typeof import('./src/components/vip/levelDown.vue')['default']
    LevelRestore: typeof import('./src/components/vip/levelRestore.vue')['default']
    LevelUp: typeof import('./src/components/vip/levelUp.vue')['default']
    LifeRank: typeof import('./src/components/earnMoney/lifeRank.vue')['default']
    List: typeof import('./src/components/me/list.vue')['default']
    Loading: typeof import('./src/components/Loading.vue')['default']
    LoseReturn: typeof import('./src/components/dialog/loseReturn.vue')['default']
    Luckyspinerule: typeof import('./src/components/luckyspin/luckyspinerule.vue')['default']
    Media: typeof import('./src/components/newHome/media.vue')['default']
    Menu: typeof import('./src/components/newHome/menu.vue')['default']
    MenuGame: typeof import('./src/components/newHome/menuGame.vue')['default']
    MenuMix: typeof import('./src/components/newHome/menuMix.vue')['default']
    MenuSet: typeof import('./src/components/newHome/menuSet.vue')['default']
    MethodIcon: typeof import('./src/components/wallet/methodIcon.vue')['default']
    MonthRank: typeof import('./src/components/earnMoney/monthRank.vue')['default']
    MorePage: typeof import('./src/components/jackpot/MorePage.vue')['default']
    MyRewards: typeof import('./src/components/jackpot/MyRewards.vue')['default']
    NormalDialog: typeof import('./src/components/dialog/NormalDialog.vue')['default']
    NormalLayout: typeof import('./src/components/layout/normalLayout.vue')['default']
    PersonInfo: typeof import('./src/components/earnMoney/personInfo.vue')['default']
    ProductChannel: typeof import('./src/components/dialog/productChannel.vue')['default']
    ProviderSheet: typeof import('./src/components/searchGame/ProviderSheet.vue')['default']
    Rank: typeof import('./src/components/earnMoney/rank.vue')['default']
    Rank3: typeof import('./src/components/earnMoney/rank3.vue')['default']
    RankingList: typeof import('./src/components/jackpot/RankingList.vue')['default']
    RankItem: typeof import('./src/components/earnMoney/rankItem.vue')['default']
    Record: typeof import('./src/components/wallet/record.vue')['default']
    RecordList: typeof import('./src/components/invitewheel/recordList.vue')['default']
    RedeemDialog: typeof import('./src/components/dialog/redeemDialog.vue')['default']
    ReedemRecoder: typeof import('./src/components/earnMoney/reedem-recoder.vue')['default']
    Reseller: typeof import('./src/components/dialog/reseller.vue')['default']
    RewardClaimCard: typeof import('./src/components/jackpot/RewardClaimCard.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Rules: typeof import('./src/components/jackpot/Rules.vue')['default']
    RuleTable: typeof import('./src/components/vip/ruleTable.vue')['default']
    RuleTable1: typeof import('./src/components/vip/ruleTable1.vue')['default']
    ScrollList: typeof import('./src/components/ScrollList.vue')['default']
    SearchBar: typeof import('./src/components/searchGame/SearchBar.vue')['default']
    SearchGame: typeof import('./src/components/searchGame/index.vue')['default']
    SearchHeader: typeof import('./src/components/searchGame/SearchHeader.vue')['default']
    SearchHistory: typeof import('./src/components/searchGame/SearchHistory.vue')['default']
    SelectSheet: typeof import('./src/components/searchGame/SelectSheet.vue')['default']
    SeriesList: typeof import('./src/components/video/seriesList.vue')['default']
    Server: typeof import('./src/components/server.vue')['default']
    ShareDialog: typeof import('./src/components/dialog/shareDialog.vue')['default']
    Sign: typeof import('./src/components/dialog/sign.vue')['default']
    Signaward: typeof import('./src/components/dialog/signaward.vue')['default']
    SignIcon: typeof import('./src/components/newHome/signIcon.vue')['default']
    Spinbar: typeof import('./src/components/luckyspin/spinbar.vue')['default']
    SpinIcon: typeof import('./src/components/newHome/spinIcon.vue')['default']
    Spinitem: typeof import('./src/components/luckyspin/spinitem.vue')['default']
    SvgIcons: typeof import('./src/components/common/SvgIcons.vue')['default']
    System: typeof import('./src/components/notice/system/index.vue')['default']
    T1Player: typeof import('./src/components/earnMoney/t1Player.vue')['default']
    TabList: typeof import('./src/components/newHome/tabList.vue')['default']
    TimeChampion: typeof import('./src/components/jackpot/TimeChampion.vue')['default']
    Tip: typeof import('./src/components/wallet/tip.vue')['default']
    Tool: typeof import('./src/components/foru/tool.vue')['default']
    TotalEarn: typeof import('./src/components/earnMoney/totalEarn.vue')['default']
    Tranfer: typeof import('./src/components/chat/tranfer.vue')['default']
    Transaction: typeof import('./src/components/notice/transaction/index.vue')['default']
    UnderlineButton: typeof import('./src/components/common/UnderlineButton.vue')['default']
    VanActionSheet: typeof import('vant/es')['ActionSheet']
    VanBackTop: typeof import('vant/es')['BackTop']
    VanBadge: typeof import('vant/es')['Badge']
    VanButton: typeof import('vant/es')['Button']
    VanCell: typeof import('vant/es')['Cell']
    VanCheckbox: typeof import('vant/es')['Checkbox']
    VanCheckboxGroup: typeof import('vant/es')['CheckboxGroup']
    VanCountDown: typeof import('vant/es')['CountDown']
    VanDivider: typeof import('vant/es')['Divider']
    VanEmpty: typeof import('vant/es')['Empty']
    VanField: typeof import('vant/es')['Field']
    VanFloatingBubble: typeof import('vant/es')['FloatingBubble']
    VanForm: typeof import('vant/es')['Form']
    VanIcon: typeof import('vant/es')['Icon']
    VanList: typeof import('vant/es')['List']
    VanLoading: typeof import('vant/es')['Loading']
    VanOverlay: typeof import('vant/es')['Overlay']
    VanPopover: typeof import('vant/es')['Popover']
    VanPopup: typeof import('vant/es')['Popup']
    VanPullRefresh: typeof import('vant/es')['PullRefresh']
    VanSwipe: typeof import('vant/es')['Swipe']
    VanSwipeItem: typeof import('vant/es')['SwipeItem']
    VanTab: typeof import('vant/es')['Tab']
    VanTabs: typeof import('vant/es')['Tabs']
    VanTextEllipsis: typeof import('vant/es')['TextEllipsis']
    Video: typeof import('./src/components/video/video.vue')['default']
    VideoItem: typeof import('./src/components/video/videoItem.vue')['default']
    VideoTool: typeof import('./src/components/video/videoTool.vue')['default']
    VIPBenefitsLarger: typeof import('./src/components/vip/VIPBenefitsLarger.vue')['default']
    VipCard: typeof import('./src/components/jackpot/VipCard.vue')['default']
    VipCs: typeof import('./src/components/vip/vipCs.vue')['default']
    VipDialog: typeof import('./src/components/vip/vipDialog.vue')['default']
    VipHeaderOptimized: typeof import('./src/components/vip/VipHeaderOptimized.vue')['default']
    WalletHeader: typeof import('./src/components/wallet/walletHeader.vue')['default']
    WalletLamp: typeof import('./src/components/wallet/walletLamp.vue')['default']
    Welcome: typeof import('./src/components/dialog/welcome.vue')['default']
    WelcomeGift: typeof import('./src/components/dialog/welcomeGift.vue')['default']
    WelcomeSignup: typeof import('./src/components/dialog/welcomeSignup.vue')['default']
    WithDdawShare: typeof import('./src/components/wallet/withDdawShare.vue')['default']
    WithdrawRecord: typeof import('./src/components/wallet/withdrawRecord.vue')['default']
  }
}
