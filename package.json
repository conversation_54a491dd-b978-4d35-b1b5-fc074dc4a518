{"name": "BetFugu", "version": "2.11.137", "description": "BetFugu app", "scripts": {"dev": "vite --force", "test": "vite --force --mode production", "build:dev": "npm run upgrade && vite build --mode development", "build": "npm run upgrade &&  vite build", "build:release": "npm run upgrade &&  vite build --mode production", "publish:dev": "npm run build:dev && ./upload test", "publish:mac": "npm run build:release && ./upload mac", "publish:release": "npm run build:release && node ./build/publish.js --mode development", "publish": "npm run build && node ./build/publish.js --mode production", "lint-fix": "eslint --fix --ext .js --ext .jsx --ext .vue src/", "lang": "node ./build/translate.js", "upgrade": "node ./build/version.ts", "replace": "node ./build/replacekey.js", "preview": "vite preview", "copy": "node ./build/copy.js --mode development"}, "dependencies": {"@aws-sdk/client-s3": "^3.420.0", "@fingerprintjs/fingerprintjs": "^4.6.2", "@sentry/vue": "^8.22.0", "@twa-dev/sdk": "^7.10.1", "@vant/use": "^1.6.0", "@videojs/http-streaming": "^3.15.0", "@vueuse/core": "^10.11.0", "amfe-flexible": "^2.2.1", "async": "^3.2.4", "axios": "^1.3.5", "crypto-js": "^3.3.0", "dayjs": "^1.11.12", "html2canvas": "^1.4.1", "js-base64": "^3.7.7", "js-cookie": "^3.0.1", "libphonenumber-js": "^1.11.5", "md5": "^2.3.0", "mitt": "^3.0.0", "nprogress": "^0.2.0", "pinia": "^2.2.0", "pinia-plugin-persistedstate": "^3.2.1", "qs": "^6.11.1", "swiper": "^11.1.15", "url-parse": "^1.5.10", "vant": "^4.9.1", "video.js": "^8.21.0", "vue": "^3.4.35", "vue-clipboard3": "^2.0.0", "vue-cropper": "^1.1.4", "vue-i18n": "9", "vue-number-animation": "^2.0.2", "vue-qrcode": "^2.2.2", "vue-router": "^4.4.1", "vue3-marquee": "^4.2.2"}, "devDependencies": {"@inquirer/prompts": "^5.3.6", "@types/crypto-js": "^3.1.47", "@types/node": "^18.15.11", "@types/nprogress": "^0.2.0", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "@vant/auto-import-resolver": "^1.2.1", "@vitejs/plugin-basic-ssl": "^1.1.0", "@vitejs/plugin-legacy": "^5.4.2", "@vitejs/plugin-vue": "^4.1.0", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vue/compiler-sfc": "^3.4.35", "ali-oss-publish": "^0.4.0", "autoprefixer": "^10.4.19", "commander": "^12.1.0", "eruda": "^3.2.1", "eslint": "^8.38.0", "eslint-plugin-vue": "^9.10.0", "inquirer": "^10.1.7", "mockjs": "^1.1.0", "postcss": "^8.4.41", "postcss-preset-env": "^9.5.15", "postcss-pxtorem": "^5.1.1", "prettier": "^2.8.7", "rollup-plugin-visualizer": "^5.14.0", "sass": "^1.61.0", "tailwindcss": "^3.4.4", "terser": "^5.34.0", "typescript": "^5.0.4", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.27.2", "vite": "^5.0.0", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-pwa": "^0.20.1", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vue-eslint-parser": "^9.1.1", "workbox-window": "^7.3.0", "xlsx": "^0.18.5"}, "browserslist": ["last 2 versions", "not dead", "> 0.3%", "not op_mini all", "last 3 version", "Chrome >= 51", "Edge >= 15", "Safari >= 10", "Firefox >= 54", "Opera >= 38", "iOS >= 10", "Android >= 5", "not IE <= 11"], "larkSheet": "https://qjjkwz2eagx.sg.larksuite.com/sheets", "sheetName": "MP2YsirRjhlpRGt0xuHlNMt8grg", "sheetId": ""}